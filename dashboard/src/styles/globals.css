@tailwind base;
@tailwind components;
@tailwind utilities;

/* FC Iconic Font Loading - Performance-Optimized Strategy */
/* Critical CSS inlining for instant typography rendering */
/* Medical environment optimized loading with preconnect support */

/* Critical FC Iconic Regular - Must load for brand consistency */
@font-face {
  font-family: 'FC Iconic';
  src: url('/fonts/FC Iconic Regular.otf') format('opentype'),
       url('/fonts/FC Iconic Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap; /* Critical font - always swap when loaded */
  unicode-range: U+0020-007F, U+0E01-0E3A, U+0E40-0E4E, U+0E50-0E59;
}

/* FC Iconic Italic - Progressive enhancement */
@font-face {
  font-family: 'FC Iconic';
  src: url('/fonts/FC Iconic Italic.otf') format('opentype'),
       url('/fonts/FC Iconic Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: fallback; /* Less critical - use fallback strategy */
  unicode-range: U+0020-007F, U+0E01-0E3A, U+0E40-0E4E, U+0E50-0E59;
}

/* Critical FC Iconic Bold - Essential for headings and emphasis */
@font-face {
  font-family: 'FC Iconic';
  src: url('/fonts/FC Iconic Bold.otf') format('opentype'),
       url('/fonts/FC Iconic Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap; /* Critical font - always swap when loaded */
  unicode-range: U+0020-007F, U+0E01-0E3A, U+0E40-0E4E, U+0E50-0E59;
}

/* FC Iconic Bold Italic - Progressive enhancement */
@font-face {
  font-family: 'FC Iconic';
  src: url('/fonts/FC Iconic Bold Italic.otf') format('opentype'),
       url('/fonts/FC Iconic Bold Italic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
  font-display: fallback; /* Less critical - use fallback strategy */
  unicode-range: U+0020-007F, U+0E01-0E3A, U+0E40-0E4E, U+0E50-0E59;
}

/* Font Loading State Management */
.font-loading {
  font-family: 'Noto Sans Thai', 'Noto Sans', system-ui, sans-serif;
  opacity: 0.8;
}

.fc-iconic-loaded {
  font-family: var(--font-primary);
}

.font-fallback-active {
  font-family: 'Noto Sans Thai', 'Noto Sans', system-ui, sans-serif;
}

@layer base {
  :root {
    /* ChromoForge Design System CSS Custom Properties */
    
    /* FC Iconic Font System */
    --font-primary: 'FC Iconic', 'Noto Sans Thai', 'Noto Sans', system-ui, sans-serif;
    
    /* Typography Scale */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 32px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;
    --text-4xl: 36px;
    --text-5xl: 48px;
    --text-6xl: 56px;
    --text-7xl: 72px;
    
    /* Font Weights - Strategic Usage */
    --weight-regular: 400;    /* 80% of interface - body text, navigation, forms */
    --weight-bold: 700;       /* 20% of interface - headings, CTAs, emphasis */
    
    /* Line Heights */
    --leading-tight: 1.1;
    --leading-snug: 1.2;
    --leading-normal: 1.5;
    --leading-relaxed: 1.6;
    --leading-loose: 1.7;
    
    /* Letter Spacing */
    --tracking-tighter: -0.02em;
    --tracking-tight: -0.01em;
    --tracking-normal: 0;
    --tracking-wide: 0.01em;
    --tracking-wider: 0.02em;
    --tracking-widest: 0.05em;
    
    /* Typography Colors - Optimized for FC Iconic */
    --text-primary: 19 78 74;     /* #134E4A - Dark teal for maximum readability */
    --text-secondary: 15 118 110; /* #0F766E - Medium teal for emphasis */
    --text-muted: 100 116 139;    /* #64748B - Neutral gray for supporting text */
    --text-subtle: 148 163 184;   /* #94A3B8 - Light gray for captions */
    
    /* Interactive Text Colors */
    --text-link: 20 184 166;      /* #14B8A6 - Teal for links */
    --text-link-hover: 15 118 110; /* #0F766E - Darker on hover */
    --text-button: 255 255 255;   /* #FFFFFF - White on colored buttons */
    
    /* Status Text Colors */
    --text-success: 5 150 105;    /* #059669 - Success messages */
    --text-warning: 217 119 6;    /* #D97706 - Warning messages */
    --text-error: 220 38 38;      /* #DC2626 - Error messages */
    --text-info: 3 105 161;       /* #0369A1 - Information messages */
    
    /* Primary Teal System */
    --color-teal-900: 19 78 74;     /* #134E4A */
    --color-teal-700: 15 118 110;   /* #0F766E - Deep Medical Teal */
    --color-teal-500: 20 184 166;   /* #14B8A6 - ChromaForge Teal */
    --color-teal-300: 94 234 212;   /* #5EEAD4 - Mint Gradient */
    --color-teal-100: 204 251 241;  /* #CCFBF1 */
    --color-teal-50: 240 253 250;   /* #F0FDFA */
    
    /* Accent Colors */
    --color-lime: 132 204 22;       /* #84CC16 - BioLime */
    --color-amber: 245 158 11;      /* #F59E0B - Genome Gold */
    
    /* Professional Grays */
    --color-gray-700: 55 65 81;     /* #374151 - Research Charcoal */
    --color-gray-500: 107 114 128;  /* #6B7280 - Lab Gray */
    --color-gray-100: 243 244 246;  /* #F3F4F6 - Clinical Silver */
    --color-white: 255 255 255;     /* #FFFFFF */
    
    /* Semantic Colors */
    --color-success: 16 185 129;    /* #10B981 */
    --color-warning: 245 158 11;    /* #F59E0B */
    --color-error: 239 68 68;       /* #EF4444 */
    --color-info: 59 130 246;       /* #3B82F6 */
    
    /* Base CSS Variables for shadcn/ui */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 174 100% 29%;         /* Deep Medical Teal */
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 174 100% 29%;            /* Deep Medical Teal */
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 174 100% 50%;         /* ChromaForge Teal for dark mode */
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 174 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1, "kern" 1;
    /* Enhanced rendering for medical environments */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Critical font loading optimization */
    font-synthesis: none;
  }
  
  /* Enhanced ChromoForge Typography System - FC Iconic Powered */
  body {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    line-height: var(--leading-snug);
    color: rgb(var(--text-primary));
  }
  
  /* ChromoForge Logo System - FC Iconic Implementation */
  .chromoforge-logo {
    font-family: var(--font-primary);
    font-weight: var(--weight-bold); /* Bold for brand authority */
    text-transform: uppercase;
    letter-spacing: 0.02em;
    line-height: 1;
  }
  
  /* Logo Variants */
  .logo-hero {
    font-size: var(--text-6xl);
    font-weight: var(--weight-bold);
    letter-spacing: var(--tracking-tight);
  }
  
  .logo-header {
    font-size: var(--text-3xl);
    font-weight: var(--weight-bold);
    letter-spacing: var(--tracking-wide);
  }
  
  .logo-compact {
    font-size: var(--text-xl);
    font-weight: var(--weight-bold);
    letter-spacing: var(--tracking-wide);
  }
  
  .logo-footer {
    font-size: var(--text-2xl);
    font-weight: var(--weight-regular);
    letter-spacing: var(--tracking-normal);
  }
  
  /* Medical-grade focus indicators - Enhanced UX */
  .focus-visible {
    @apply outline-none ring-2 ring-lime-500 ring-offset-2;
    /* Smooth focus transitions for professional feel */
    transition: box-shadow 150ms cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 150ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: scale(1.01);
  }
  
  /* Enhanced touch targets for mobile medical forms */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    padding: 8px;
  }
  
  /* Professional hover states */
  .hover-lift {
    transition: transform 200ms ease-out, box-shadow 200ms ease-out;
  }
  
  .hover-lift:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  /* Professional medical interface transitions */
  .transition-medical {
    transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  
  .transition-focus {
    transition: box-shadow 150ms ease-out, outline 150ms ease-out, transform 150ms ease-out;
  }
  
  .transition-interactive {
    transition: color 200ms ease-out, background-color 200ms ease-out, border-color 200ms ease-out;
  }
  
  /* ChromoForge gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, rgb(var(--color-teal-700)) 0%, rgb(var(--color-teal-500)) 50%, rgb(var(--color-teal-300)) 100%);
  }
  
  .gradient-background {
    background: linear-gradient(180deg, rgb(var(--color-teal-50)) 0%, rgb(var(--color-teal-100)) 100%);
  }
  
  .gradient-hero {
    background: linear-gradient(45deg, rgb(var(--color-teal-700)) 0%, rgb(var(--color-teal-500)) 100%);
  }
  
  /* Medical-grade shadows */
  .shadow-card {
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  }
  
  .shadow-modal {
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  }
  
  .shadow-dropdown {
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  }
  
  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* Accessibility Enhancements for FC Iconic */
  
  /* Enhanced Focus States */
  .focus-ring {
    outline: 2px solid rgb(var(--color-lime));
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(132, 204, 22, 0.1);
  }
  
  /* WCAG 2.1 AA+ Compliance - Enhanced Accessibility */
  @media (prefers-contrast: high) {
    .border {
      @apply border-2;
    }
    
    .text-primary { 
      color: #000000;
      font-weight: 500; /* Slightly bolder for better readability */
    }
    
    .text-secondary { 
      color: #1F2937;
      font-weight: 500;
    }
    
    .btn-primary { 
      background: #000000;
      border: 3px solid #000000; /* Thicker borders for medical environments */
      font-weight: var(--weight-bold);
      box-shadow: 0 0 0 2px white, 0 0 0 4px #000000;
    }
    
    /* Enhanced focus indicators in high contrast */
    .focus-visible {
      outline: 4px solid #000000;
      outline-offset: 2px;
    }
  }
  
  /* Medical environment lighting optimizations */
  @media (prefers-color-scheme: light) {
    :root {
      --text-contrast-boost: 1.1;
    }
    
    .text-critical {
      color: #B91C1C; /* Enhanced red for critical information */
      font-weight: var(--weight-bold);
      text-shadow: 0 0 1px rgba(185, 28, 28, 0.2);
    }
  }
  
  @media (prefers-color-scheme: dark) {
    :root {
      --text-contrast-boost: 1.2;
    }
    
    .text-critical {
      color: #FCA5A5; /* Softer red for dark mode medical environments */
      font-weight: var(--weight-bold);
    }
  }
  
  /* Screen Reader Optimization */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  .sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    padding: 8px;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
    font-family: var(--font-primary);
    font-weight: var(--weight-bold);
    background: rgb(var(--color-lime));
    color: #000000;
  }
  
  /* Mobile-First Medical Interface Typography */
  @media (max-width: 640px) {
    /* Enhanced readability for mobile medical professionals */
    .text-hero { 
      font-size: var(--text-4xl); 
      font-weight: var(--weight-bold);
      line-height: 1.1;
    }
    
    .text-h1 { 
      font-size: 28px; 
      font-weight: var(--weight-bold);
      line-height: 1.2;
    }
    
    .text-h2 { 
      font-size: var(--text-2xl); 
      font-weight: var(--weight-bold);
      line-height: 1.25;
    }
    
    .text-body { 
      font-size: var(--text-base); 
      font-weight: var(--weight-regular);
      line-height: 1.6; /* Optimized for medical content scanning */
    }
    
    .logo-header { 
      font-size: var(--text-2xl); 
      font-weight: var(--weight-bold);
    }
    
    /* Mobile medical form optimizations */
    .medical-form-mobile {
      font-size: 16px; /* Prevents zoom on iOS */
      line-height: 1.4;
      padding: 12px 16px;
    }
    
    /* Enhanced Thai text spacing on mobile */
    .thai-text-mobile {
      line-height: 1.8;
      letter-spacing: 0.01em;
    }
  }
  
  @media (min-width: 641px) and (max-width: 1024px) {
    .text-hero { 
      font-size: var(--text-5xl); 
      font-weight: var(--weight-bold); 
    }
    
    .text-h1 { 
      font-size: 32px; 
      font-weight: var(--weight-bold); 
    }
    
    .text-h2 { 
      font-size: 28px; 
      font-weight: var(--weight-bold); 
    }
    
    .logo-header { 
      font-size: 28px; 
      font-weight: var(--weight-bold); 
    }
  }
  
  @media (min-width: 1025px) {
    .text-ultra-hero { 
      font-size: var(--text-7xl); 
      font-weight: var(--weight-bold); 
    }
    
    .text-hero { 
      font-size: var(--text-6xl); 
      font-weight: var(--weight-bold); 
    }
    
    .text-h1 { 
      font-size: var(--text-4xl); 
      font-weight: var(--weight-bold); 
    }
    
    .logo-hero { 
      font-size: var(--text-6xl); 
      font-weight: var(--weight-bold); 
    }
  }
  
  /* Medical document print optimization */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-break {
      page-break-before: always;
    }
    
    .print-avoid-break {
      page-break-inside: avoid;
    }
    
    /* Medical typography print optimization */
    * {
      background: white !important;
      color: black !important;
      text-shadow: none !important;
    }
    
    .medical-critical {
      font-weight: var(--weight-bold) !important;
      font-size: 110% !important;
    }
    
    .patient-info {
      border: 2px solid black !important;
      padding: 8px !important;
      margin: 4px 0 !important;
    }
  }
}

/* Performance-Critical Font Loading Indicators */
.font-loading-indicator {
  position: relative;
  opacity: 0.7;
}

.font-loading-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(20, 184, 166, 0.1) 50%,
    transparent 100%
  );
  animation: font-loading-shimmer 1.5s ease-in-out infinite;
}

@keyframes font-loading-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Critical CSS for instant rendering */
.critical-text {
  font-family: system-ui, -apple-system, sans-serif;
  font-weight: 400;
}

.fc-iconic-loaded .critical-text {
  font-family: var(--font-primary);
}

/* FC Iconic Typography Hierarchy - Design System Implementation */
@layer components {
  /* Display Typography - Maximum Brand Impact */
  .text-ultra-hero {
    font-family: var(--font-primary);
    font-weight: var(--weight-bold);
    font-size: var(--text-7xl);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tighter);
    text-transform: uppercase;
    color: rgb(var(--color-teal-700));
  }
  
  .text-hero {
    font-family: var(--font-primary);
    font-weight: var(--weight-bold);
    font-size: var(--text-6xl);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
    color: rgb(var(--color-teal-700));
  }
  
  .text-display-lg {
    font-family: var(--font-primary);
    font-weight: var(--weight-bold);
    font-size: var(--text-5xl);
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tight);
    color: rgb(var(--color-teal-700));
  }
  
  /* Heading Hierarchy - Strategic Bold Usage */
  .text-h1 {
    font-family: var(--font-primary);
    font-weight: var(--weight-bold);
    font-size: var(--text-4xl);
    line-height: var(--leading-snug);
    letter-spacing: var(--tracking-normal);
    color: rgb(var(--text-primary));
  }
  
  .text-h2 {
    font-family: var(--font-primary);
    font-weight: var(--weight-bold);
    font-size: var(--text-3xl);
    line-height: var(--leading-snug);
    letter-spacing: var(--tracking-normal);
    color: rgb(var(--text-primary));
  }
  
  .text-h3 {
    font-family: var(--font-primary);
    font-weight: var(--weight-bold);
    font-size: var(--text-2xl);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
    color: rgb(var(--text-secondary));
  }
  
  .text-h4 {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-xl);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-wide);
    text-transform: uppercase;
    color: rgb(var(--text-primary));
  }
  
  .text-h5 {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-lg);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-wider);
    text-transform: uppercase;
    color: rgb(var(--text-primary));
  }
  
  .text-h6 {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-widest);
    text-transform: uppercase;
    color: rgb(var(--color-teal-500));
  }
  
  /* Body Content - Regular Weight Optimization */
  .text-lead {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-xl);
    line-height: var(--leading-loose);
    letter-spacing: var(--tracking-wide);
    color: rgb(var(--text-primary));
  }
  
  .text-body-lg {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-lg);
    line-height: var(--leading-loose);
    letter-spacing: var(--tracking-normal);
    color: rgb(var(--text-primary));
  }
  
  .text-body {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    letter-spacing: var(--tracking-normal);
    color: rgb(var(--text-primary));
  }
  
  .text-body-sm {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
    letter-spacing: var(--tracking-wide);
    color: rgb(var(--text-secondary));
  }
  
  .text-caption {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-style: italic;
    font-size: var(--text-xs);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-wider);
    color: rgb(var(--text-muted));
  }
  
  /* Interactive Elements - Strategic Bold Application */
  .btn-text-lg {
    font-family: var(--font-primary);
    font-weight: var(--weight-bold);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-wide);
    text-transform: uppercase;
  }
  
  .btn-text-md {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-wider);
    text-transform: uppercase;
  }
  
  .btn-text-sm {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-xs);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-widest);
    text-transform: uppercase;
  }
  
  /* Navigation Text */
  .nav-text {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-wide);
  }
  
  .nav-text--active {
    font-weight: var(--weight-bold);
  }
  
  /* Form Elements */
  .label-text {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-wider);
    text-transform: uppercase;
    color: rgb(var(--text-secondary));
  }
  
  .input-text {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
  }
  
  .placeholder-text {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-style: italic;
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
    color: rgb(var(--text-muted));
  }
  
  /* Emphasis and Special Cases */
  .text-strong {
    font-weight: var(--weight-bold);
  }
  
  .text-emphasis {
    font-style: italic;
    color: rgb(var(--color-teal-500));
  }
  
  .text-quote {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-style: italic;
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    letter-spacing: var(--tracking-wide);
    color: rgb(var(--text-secondary));
  }
  
  .text-code {
    font-family: var(--font-primary);
    font-weight: var(--weight-regular);
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-widest);
    background: rgb(var(--color-teal-50));
    padding: 2px 6px;
    border-radius: 4px;
  }
}