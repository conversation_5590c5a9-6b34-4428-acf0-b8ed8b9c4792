/* ChromoForge Advanced Typography Styles
   Innovative CSS for medical interface typography */

/* Thai-Latin Boundary Optimization */
.thai-english-optimized {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.lang-boundary-space {
  display: inline-block;
  width: 0.15em;
  /* Micro-adjustment for visual balance */
  margin-left: -0.05em;
  margin-right: -0.05em;
}

/* Enhanced Dynamic Typography with Medical Context Awareness */
.medical-typography {
  font-size: var(--dynamic-font-size, 1rem);
  line-height: calc(var(--dynamic-line-height, 1.6) * 1em);
  letter-spacing: var(--dynamic-letter-spacing, 0);
  /* Smooth professional transitions */
  transition: font-size 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
              color 200ms ease-out,
              line-height 200ms ease-out;
  /* Enhanced rendering for medical precision */
  font-variant-numeric: tabular-nums;
  /* Improved medical data alignment */
  font-feature-settings: "tnum" 1, "kern" 1;
}

/* Context-aware scaling for medical content */
@supports (font-size: clamp(1rem, 2vw, 1.5rem)) {
  .medical-typography {
    font-size: clamp(
      calc(var(--dynamic-font-size, 1rem) * 0.85),
      calc(var(--dynamic-font-size, 1rem) * 1vw),
      calc(var(--dynamic-font-size, 1rem) * 1.15)
    );
  }
}

/* Progressive Font Loading States */
.font-loader-wrapper {
  position: relative;
  min-height: 1em;
}

.font-loading {
  /* Skeleton animation while loading */
  background: linear-gradient(
    90deg,
    rgba(var(--color-teal-100), 0.3) 0%,
    rgba(var(--color-teal-100), 0.5) 50%,
    rgba(var(--color-teal-100), 0.3) 100%
  );
  background-size: 200% 100%;
  animation: font-skeleton 1.5s ease-in-out infinite;
  color: transparent;
}

@keyframes font-skeleton {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.font-fallback {
  /* Fallback font optimization */
  font-family: 'Noto Sans Thai', 'Noto Sans', system-ui, sans-serif;
  letter-spacing: -0.01em; /* Compensate for different metrics */
}

/* Enhanced Adaptive Contrast System for Medical Environments */
.adaptive-contrast {
  transition: color 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
              text-shadow 200ms ease-out;
  /* Medical environment lighting adaptations */
  filter: contrast(var(--contrast-adjust, 1));
}

@supports (color: color-contrast(white vs black)) {
  .adaptive-contrast {
    color: color-contrast(
      var(--background-color, white) 
      vs 
      rgb(var(--color-teal-900)),
      rgb(var(--color-teal-700)),
      rgb(var(--color-teal-500)),
      rgb(var(--color-teal-300))
    );
  }
}

/* Medical environment contrast enhancements */
.medical-high-contrast {
  color: rgb(var(--text-primary));
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

.medical-low-light {
  color: rgb(var(--color-teal-300));
  text-shadow: 0 0 2px rgba(var(--color-teal-300), 0.3);
}

.medical-bright-light {
  color: rgb(var(--color-teal-900));
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
  font-weight: 500;
}

/* Smart Focus Management */
.smart-focusable {
  position: relative;
  outline: none;
  transition: transform 150ms ease-out;
}

.smart-focusable:focus-within {
  transform: scale(var(--focus-scale, 1.02));
}

.smart-focusable.focused::before {
  content: '';
  position: absolute;
  inset: -4px;
  border: 2px solid rgb(var(--color-lime));
  border-radius: 8px;
  opacity: 0;
  animation: focus-ring-appear 200ms ease-out forwards;
}

@keyframes focus-ring-appear {
  to {
    opacity: 1;
    inset: -8px;
  }
}

/* Focus priority visual indicators */
.focus-priority-9::before,
.focus-priority-8::before,
.focus-priority-7::before {
  border-color: rgb(var(--color-error));
  border-width: 3px;
}

/* Advanced Variable Font Weight for Medical Interface States */
@supports (font-variation-settings: 'wght' 400) {
  .variable-weight {
    font-variation-settings: 'wght' var(--font-weight, 400);
    transition: font-variation-settings 180ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  
  .variable-weight-animated {
    animation: medical-emphasis 2.5s ease-in-out infinite;
  }
  
  /* Medical attention animation */
  .medical-attention-weight {
    animation: medical-critical-weight 1.8s ease-in-out infinite;
  }
  
  @keyframes medical-emphasis {
    0%, 100% { 
      font-variation-settings: 'wght' 400;
      color: rgb(var(--text-primary));
    }
    50% { 
      font-variation-settings: 'wght' 550;
      color: rgb(var(--color-teal-600));
    }
  }
  
  @keyframes medical-critical-weight {
    0%, 100% { 
      font-variation-settings: 'wght' 500;
    }
    50% { 
      font-variation-settings: 'wght' 700;
    }
  }
  
  /* Interactive weight adjustments */
  .variable-weight:hover {
    font-variation-settings: 'wght' calc(var(--font-weight, 400) + 50);
  }
  
  .variable-weight:focus {
    font-variation-settings: 'wght' calc(var(--font-weight, 400) + 100);
  }
}

/* Medical Priority Animations */
.animate-pulse-subtle {
  animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-subtle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Advanced Medical Data Density Modes */
.density-compact {
  --dynamic-line-height: 1.3;
  --dynamic-letter-spacing: -0.01em;
  --dynamic-base-size: 14px;
}

.density-normal {
  --dynamic-line-height: 1.6;
  --dynamic-letter-spacing: 0;
  --dynamic-base-size: 16px;
}

.density-comfortable {
  --dynamic-line-height: 1.8;
  --dynamic-letter-spacing: 0.02em;
  --dynamic-base-size: 18px;
}

/* Viewing Condition Adaptations */
@media (prefers-contrast: high) {
  .adaptive-contrast {
    --contrast-adjust: 1.5;
    font-weight: 500;
  }
}

@media (prefers-contrast: low) {
  .adaptive-contrast {
    --contrast-adjust: 0.8;
  }
}

/* Light/Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .medical-typography {
    --dynamic-letter-spacing: 0.02em; /* Slightly wider for dark mode */
  }
  
  .adaptive-contrast {
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.1); /* Subtle glow for readability */
  }
}

/* Advanced Performance Optimizations for Medical Interfaces */
.typography-gpu-accelerated {
  will-change: transform, opacity;
  transform: translateZ(0); /* Force GPU layer */
  backface-visibility: hidden;
  /* Optimized for medical dashboard performance */
  contain: layout style paint;
}

/* Critical performance classes for medical data */
.medical-data-optimized {
  contain: layout style;
  /* Prevent layout thrashing in data-heavy medical interfaces */
  will-change: auto;
}

/* Smooth scrolling for medical record review */
.medical-scroll-container {
  scroll-behavior: smooth;
  /* Enhanced scrolling for medical document review */
  overscroll-behavior: contain;
}

/* Memory-efficient medical typography */
.medical-text-efficient {
  /* Prevent font subsetting issues */
  font-synthesis: none;
  /* Optimize rendering pipeline */
  text-rendering: geometricPrecision;
}

/* Medical Form Typography Enhancements */
.medical-form-field {
  position: relative;
}

.medical-form-field:focus-within .field-label {
  transform: translateY(-1.5em) scale(0.85);
  color: rgb(var(--color-teal-500));
  font-weight: 500;
}

.field-label {
  position: absolute;
  top: 0.75em;
  left: 0.75em;
  transition: all 200ms ease-out;
  pointer-events: none;
}

/* Responsive Thai Typography */
@media (max-width: 640px) {
  .thai-text {
    /* Adjust line height for better Thai readability on mobile */
    line-height: 1.8;
    word-break: break-word;
  }
}

/* Print Optimizations for Medical Documents */
@media print {
  .medical-typography {
    font-size: 11pt;
    line-height: 1.4;
    color: black;
  }
  
  .no-print {
    display: none !important;
  }
  
  /* Force black text for maximum contrast */
  .adaptive-contrast {
    color: black !important;
    text-shadow: none !important;
  }
}

/* Experimental: Container Queries for Typography */
@container (min-width: 400px) {
  .container-responsive-text {
    font-size: calc(1rem + 0.5cqi);
  }
}

@container (min-width: 800px) {
  .container-responsive-text {
    font-size: calc(1.2rem + 0.3cqi);
  }
}

/* Future-Ready: CSS Nesting for Typography Components */
.medical-data-card {
  .card-header {
    font-size: var(--text-h4);
    font-weight: var(--weight-bold);
    
    &:hover {
      color: rgb(var(--color-teal-500));
    }
  }
  
  .card-content {
    font-size: var(--text-body);
    line-height: var(--leading-relaxed);
    
    .highlight {
      background: linear-gradient(
        transparent 40%,
        rgba(var(--color-lime), 0.3) 40%,
        rgba(var(--color-lime), 0.3) 90%,
        transparent 90%
      );
    }
  }
}

/* Advanced Kerning for Thai-English Combinations */
.thai-english-kerning {
  font-kerning: normal;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
}

/* Special handling for Thai tone marks */
.thai-with-tones {
  line-height: 1.8; /* Extra space for tone marks */
  padding-top: 0.1em; /* Prevent clipping */
}

/* Medical Abbreviation Handling */
.medical-abbr {
  font-feature-settings: "c2sc" 1; /* Small caps for abbreviations */
  letter-spacing: 0.05em;
  font-weight: 500;
}

/* Additional Medical Interface Enhancements */

/* Critical medical information styling */
.medical-critical-text {
  color: rgb(var(--color-error));
  font-weight: var(--weight-bold);
  background: rgba(var(--color-error), 0.05);
  border-left: 4px solid rgb(var(--color-error));
  padding: 8px 12px;
  border-radius: 4px;
  animation: medical-critical 2s ease-in-out infinite;
}

/* Patient information styling */
.patient-info-text {
  background: rgba(var(--color-teal-100), 0.3);
  border: 1px solid rgb(var(--color-teal-300));
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 500;
}

/* Medical data table typography */
.medical-data-table {
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum" 1, "kern" 1;
}

.medical-data-table th {
  font-weight: var(--weight-bold);
  font-size: 0.9em;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: rgb(var(--text-secondary));
}

.medical-data-table td {
  font-weight: var(--weight-regular);
  font-size: 0.95em;
  line-height: 1.4;
}

/* Medical form enhancements */
.medical-form-section {
  border-top: 2px solid rgb(var(--color-teal-200));
  padding-top: 16px;
  margin-top: 24px;
}

.medical-form-section:first-child {
  border-top: none;
  padding-top: 0;
  margin-top: 0;
}

.medical-form-label {
  font-weight: 500;
  color: rgb(var(--text-primary));
  margin-bottom: 4px;
  display: block;
  font-size: 0.9em;
  text-transform: uppercase;
  letter-spacing: 0.02em;
}

.medical-form-help {
  font-size: 0.85em;
  color: rgb(var(--text-muted));
  font-style: italic;
  margin-top: 4px;
  line-height: 1.4;
}

/* Diagnostic result styling */
.diagnostic-result {
  padding: 16px;
  border-radius: 8px;
  margin: 8px 0;
  border-left: 4px solid;
}

.diagnostic-result.normal {
  background: rgba(var(--color-success), 0.05);
  border-left-color: rgb(var(--color-success));
  color: rgb(var(--text-primary));
}

.diagnostic-result.abnormal {
  background: rgba(var(--color-warning), 0.05);
  border-left-color: rgb(var(--color-warning));
  color: rgb(var(--text-primary));
}

.diagnostic-result.critical {
  background: rgba(var(--color-error), 0.05);
  border-left-color: rgb(var(--color-error));
  color: rgb(var(--text-primary));
  font-weight: var(--weight-bold);
}

/* Medical status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.85em;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.02em;
}

.status-indicator.active {
  background: rgba(var(--color-success), 0.15);
  color: rgb(var(--color-success));
}

.status-indicator.pending {
  background: rgba(var(--color-warning), 0.15);
  color: rgb(var(--color-warning));
}

.status-indicator.inactive {
  background: rgba(var(--text-muted), 0.15);
  color: rgb(var(--text-muted));
}

/* Medical alert styling */
.medical-alert {
  padding: 12px 16px;
  border-radius: 8px;
  margin: 16px 0;
  border: 1px solid;
  position: relative;
}

.medical-alert::before {
  content: '!';
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-weight: var(--weight-bold);
  font-size: 1.2em;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.medical-alert.info {
  background: rgba(var(--color-info), 0.05);
  border-color: rgb(var(--color-info));
  color: rgb(var(--text-primary));
  padding-left: 50px;
}

.medical-alert.info::before {
  background: rgb(var(--color-info));
  content: 'i';
}

.medical-alert.warning {
  background: rgba(var(--color-warning), 0.05);
  border-color: rgb(var(--color-warning));
  color: rgb(var(--text-primary));
  padding-left: 50px;
}

.medical-alert.warning::before {
  background: rgb(var(--color-warning));
}

.medical-alert.error {
  background: rgba(var(--color-error), 0.05);
  border-color: rgb(var(--color-error));
  color: rgb(var(--text-primary));
  padding-left: 50px;
  animation: medical-attention 3s ease-in-out infinite;
}

.medical-alert.error::before {
  background: rgb(var(--color-error));
}

/* Medical timeline typography */
.medical-timeline {
  position: relative;
  padding-left: 32px;
}

.medical-timeline::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgb(var(--color-teal-200));
}

.medical-timeline-item {
  position: relative;
  padding-bottom: 24px;
}

.medical-timeline-item::before {
  content: '';
  position: absolute;
  left: -26px;
  top: 6px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgb(var(--color-teal-500));
  border: 2px solid white;
  box-shadow: 0 0 0 2px rgb(var(--color-teal-500));
}

.medical-timeline-time {
  font-size: 0.85em;
  color: rgb(var(--text-muted));
  font-weight: 500;
  margin-bottom: 4px;
}

.medical-timeline-content {
  font-size: 0.95em;
  line-height: 1.5;
  color: rgb(var(--text-primary));
}

/* Medical measurement styling */
.medical-measurement {
  display: inline-flex;
  align-items: baseline;
  gap: 2px;
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum" 1;
}

.medical-measurement-value {
  font-weight: var(--weight-bold);
  font-size: 1.1em;
  color: rgb(var(--text-primary));
}

.medical-measurement-unit {
  font-size: 0.85em;
  color: rgb(var(--text-secondary));
  font-weight: var(--weight-regular);
}

.medical-measurement-range {
  font-size: 0.8em;
  color: rgb(var(--text-muted));
  margin-left: 8px;
}

/* Medical card typography */
.medical-card {
  background: white;
  border: 1px solid rgb(var(--color-teal-100));
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 200ms ease-out;
}

.medical-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.medical-card-header {
  border-bottom: 1px solid rgb(var(--color-teal-100));
  padding-bottom: 12px;
  margin-bottom: 16px;
}

.medical-card-title {
  font-size: 1.1em;
  font-weight: var(--weight-bold);
  color: rgb(var(--text-primary));
  margin-bottom: 4px;
}

.medical-card-subtitle {
  font-size: 0.9em;
  color: rgb(var(--text-secondary));
  font-weight: var(--weight-regular);
}

.medical-card-content {
  line-height: 1.6;
  color: rgb(var(--text-primary));
}

/* Loading states for medical data */
.medical-skeleton {
  background: linear-gradient(
    90deg,
    rgba(var(--color-teal-100), 0.2) 0%,
    rgba(var(--color-teal-100), 0.4) 50%,
    rgba(var(--color-teal-100), 0.2) 100%
  );
  background-size: 200% 100%;
  animation: medical-skeleton-pulse 1.5s ease-in-out infinite;
  border-radius: 4px;
  color: transparent;
}

@keyframes medical-skeleton-pulse {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Medical error states */
.medical-error-text {
  color: rgb(var(--color-error));
  font-weight: 500;
  background: rgba(var(--color-error), 0.05);
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid rgb(var(--color-error));
}

/* Medical success states */
.medical-success-text {
  color: rgb(var(--color-success));
  font-weight: 500;
  background: rgba(var(--color-success), 0.05);
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid rgb(var(--color-success));
}