/**
 * Typography Test Page
 * Dedicated page for testing CSS custom property hot-reload
 */

import React from 'react'
import { TypographyTest } from '@/components/debug/TypographyTest'

export const TypographyTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-neutral-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-primary-700 mb-2">
            CSS Custom Property Test
          </h1>
          <p className="text-lg text-neutral-600">
            Verify that CSS custom property changes are reflected in real-time
          </p>
        </div>
        
        <TypographyTest />
      </div>
    </div>
  )
}

export default TypographyTestPage
