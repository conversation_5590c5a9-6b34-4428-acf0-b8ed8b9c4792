/**
 * ChromoForge Typography Innovation Showcase
 * Demonstrates breakthrough typography features for medical interfaces
 */

import React from 'react'
import { 
  ThaiEnglishOptimizer,
  MedicalTypography,
  ProgressiveFontLoader,
  AdaptiveContrastText,
  SmartFocusableText,
  useVariableFontAnimation,
  MedicalTermSubsetter,
  TypographyPerformanceMonitor,
  TypographyVariablesProvider,
  TypographyContext
} from '@/components/ui/typography-advanced'
import { typographyEngine } from '@/lib/typography-engine'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import '../styles/typography-advanced.css'

export default function TypographyInnovationPage() {
  const [density, setDensity] = React.useState<'compact' | 'normal' | 'comfortable'>('normal')
  const [viewingConditions, setViewingConditions] = React.useState<'optimal' | 'bright' | 'dim'>('optimal')
  const [fontMetrics, setFontMetrics] = React.useState<any>(null)
  const [performanceMetrics, setPerformanceMetrics] = React.useState<any[]>([])
  
  // Variable font animation demo
  const animatedWeight = useVariableFontAnimation(400, 'breathe')
  
  // Medical terms for subsetting demo
  const medicalTerms = [
    'โรคเบาหวาน', 'ความดันโลหิตสูง', 'Hemoglobin A1c',
    'Creatinine', 'eGFR', 'การตรวจเลือด', 'ผลตรวจปัสสาวะ',
    'Cholesterol', 'Triglycerides', 'อัตราการเต้นหัวใจ'
  ]
  
  React.useEffect(() => {
    // Calculate font metrics
    typographyEngine.calculateFontMetrics('FC Iconic', 16).then(metrics => {
      setFontMetrics(metrics)
    })
    
    // Optimize medical terminology
    typographyEngine.optimizeMedicalTerminology(medicalTerms).then(optimization => {
      console.log('Medical term optimization:', optimization)
    })
  }, [])
  
  const contextValue = React.useMemo(() => ({
    density,
    contentComplexity: 'complex' as const,
    viewingConditions,
    userPreferences: {
      readingSpeed: 'normal' as const,
      focusMode: false,
      highContrast: false
    }
  }), [density, viewingConditions])
  
  return (
    <TypographyContext.Provider value={contextValue}>
      <ProgressiveFontLoader>
        <TypographyVariablesProvider config={{
          baseSize: 16,
          scaleRatio: 1.25,
          lineHeight: 1.6,
          letterSpacing: 0,
          thaiLatinSpacing: 0.1,
          medicalTermWeight: 500,
          focusScale: 1.05,
          contrastAdjust: 1
        }}>
          <div className="min-h-screen bg-gradient-to-br from-teal-50 to-white p-8">
            {/* Header */}
            <div className="max-w-7xl mx-auto mb-12">
              <h1 className="text-h1 text-center mb-4">
                Typography Innovation Lab
              </h1>
              <p className="text-lead text-center text-neutral-600">
                Breakthrough solutions for medical interface typography
              </p>
            </div>
            
            {/* Control Panel */}
            <Card className="max-w-4xl mx-auto mb-8 p-6">
              <h2 className="text-h3 mb-4">Typography Controls</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="label-text block mb-2">Content Density</label>
                  <select 
                    value={density}
                    onChange={(e) => setDensity(e.target.value as any)}
                    className="w-full p-2 border rounded"
                  >
                    <option value="compact">Compact</option>
                    <option value="normal">Normal</option>
                    <option value="comfortable">Comfortable</option>
                  </select>
                </div>
                
                <div>
                  <label className="label-text block mb-2">Viewing Conditions</label>
                  <select 
                    value={viewingConditions}
                    onChange={(e) => setViewingConditions(e.target.value as any)}
                    className="w-full p-2 border rounded"
                  >
                    <option value="optimal">Optimal</option>
                    <option value="bright">Bright</option>
                    <option value="dim">Dim</option>
                  </select>
                </div>
                
                <div>
                  <label className="label-text block mb-2">Animation</label>
                  <div 
                    className="text-body"
                    style={{ fontVariationSettings: `'wght' ${animatedWeight}` }}
                  >
                    Breathing Text: {Math.round(animatedWeight)}
                  </div>
                </div>
              </div>
            </Card>
            
            {/* Thai-English Optimization Demo */}
            <Card className="max-w-4xl mx-auto mb-8 p-6">
              <h2 className="text-h3 mb-4">Thai-English Flow Optimization</h2>
              <ThaiEnglishOptimizer>
                <p className="text-body-lg">
                  ผู้ป่วยมีระดับ Hemoglobin A1c อยู่ที่ 7.5% ซึ่งบ่งชี้ว่าการควบคุม diabetes 
                  ยังต้องปรับปรุง แพทย์แนะนำให้ทาน Metformin 500mg วันละ 2 ครั้ง พร้อมกับ
                  การปรับ lifestyle modification เพื่อลดระดับ blood glucose
                </p>
              </ThaiEnglishOptimizer>
            </Card>
            
            {/* Context-Aware Medical Typography */}
            <Card className="max-w-4xl mx-auto mb-8 p-6">
              <h2 className="text-h3 mb-4">Context-Aware Medical Typography</h2>
              <div className="space-y-3">
                <div>
                  <MedicalTypography variant="diagnosis" priority="critical">
                    Critical: Acute Myocardial Infarction
                  </MedicalTypography>
                </div>
                <div>
                  <MedicalTypography variant="lab-result" priority="important">
                    Troponin I: 2.5 ng/mL (High)
                  </MedicalTypography>
                </div>
                <div>
                  <MedicalTypography variant="prescription" priority="normal">
                    Aspirin 81mg daily, Atorvastatin 40mg at bedtime
                  </MedicalTypography>
                </div>
              </div>
            </Card>
            
            {/* Adaptive Contrast Demo */}
            <Card className="max-w-4xl mx-auto mb-8 p-6 bg-gradient-to-r from-white to-gray-100">
              <h2 className="text-h3 mb-4">Adaptive Contrast System</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 bg-white rounded">
                  <AdaptiveContrastText baseContrast={7}>
                    This text automatically adjusts its contrast based on viewing conditions
                    and background color to maintain optimal readability.
                  </AdaptiveContrastText>
                </div>
                <div className="p-4 bg-gray-800 rounded">
                  <AdaptiveContrastText baseContrast={7} className="text-white">
                    Even on dark backgrounds, the contrast adapts intelligently
                    to ensure WCAG compliance and readability.
                  </AdaptiveContrastText>
                </div>
              </div>
            </Card>
            
            {/* Smart Focus Management */}
            <Card className="max-w-4xl mx-auto mb-8 p-6">
              <h2 className="text-h3 mb-4">Smart Focus Management</h2>
              <div className="space-y-4">
                <SmartFocusableText focusGroup="patient-info" priority={9}>
                  <div className="p-4 border rounded">
                    <h3 className="text-h5 mb-2">Critical Patient Information</h3>
                    <p className="text-body">Allergies: Penicillin, Sulfa drugs</p>
                  </div>
                </SmartFocusableText>
                
                <SmartFocusableText focusGroup="vitals" priority={7}>
                  <div className="p-4 border rounded">
                    <h3 className="text-h5 mb-2">Current Vitals</h3>
                    <p className="text-body">BP: 145/90, HR: 88, SpO2: 94%</p>
                  </div>
                </SmartFocusableText>
                
                <SmartFocusableText focusGroup="medications" priority={5}>
                  <div className="p-4 border rounded">
                    <h3 className="text-h5 mb-2">Current Medications</h3>
                    <p className="text-body">Lisinopril 10mg daily, Metformin 1000mg BID</p>
                  </div>
                </SmartFocusableText>
              </div>
            </Card>
            
            {/* Font Metrics Display */}
            {fontMetrics && (
              <Card className="max-w-4xl mx-auto mb-8 p-6">
                <h2 className="text-h3 mb-4">FC Iconic Font Metrics</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-body-sm">
                  <div>
                    <span className="text-muted">Avg Char Width:</span>
                    <br />
                    <span className="font-bold">{fontMetrics.averageCharWidth.toFixed(2)}px</span>
                  </div>
                  <div>
                    <span className="text-muted">x-Height:</span>
                    <br />
                    <span className="font-bold">{fontMetrics.xHeight.toFixed(2)}px</span>
                  </div>
                  <div>
                    <span className="text-muted">Cap Height:</span>
                    <br />
                    <span className="font-bold">{fontMetrics.capHeight.toFixed(2)}px</span>
                  </div>
                  <div>
                    <span className="text-muted">Thai Height:</span>
                    <br />
                    <span className="font-bold">{fontMetrics.thaiHeight.toFixed(2)}px</span>
                  </div>
                </div>
              </Card>
            )}
            
            {/* Performance Metrics */}
            <Card className="max-w-4xl mx-auto mb-8 p-6">
              <h2 className="text-h3 mb-4">Typography Performance</h2>
              <TypographyPerformanceMonitor 
                onMetrics={(metrics) => {
                  setPerformanceMetrics(prev => [...prev.slice(-9), metrics])
                }}
              />
              {performanceMetrics.length > 0 && (
                <div className="space-y-2">
                  {performanceMetrics.map((metric, i) => (
                    <div key={i} className="flex justify-between text-body-sm">
                      <span>{metric.fontFamily}</span>
                      <span>{metric.fontLoadTime.toFixed(2)}ms load / {metric.renderTime.toFixed(2)}ms render</span>
                    </div>
                  ))}
                </div>
              )}
            </Card>
            
            {/* Medical Term Subsetting */}
            <Card className="max-w-4xl mx-auto mb-8 p-6">
              <h2 className="text-h3 mb-4">Medical Term Font Subsetting</h2>
              <MedicalTermSubsetter 
                terms={medicalTerms}
                onSubsetReady={(subset) => {
                  console.log('Generated unicode range for medical terms:', subset)
                }}
              />
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-4">
                {medicalTerms.map((term, i) => (
                  <div key={i} className="p-2 bg-teal-50 rounded text-body-sm">
                    {term}
                  </div>
                ))}
              </div>
            </Card>
            
            {/* Future-Ready Features */}
            <Card className="max-w-4xl mx-auto p-6">
              <h2 className="text-h3 mb-4">Future-Ready Typography</h2>
              <div className="space-y-4">
                <div className="medical-data-card p-4 border rounded">
                  <div className="card-header mb-2">Advanced CSS Nesting</div>
                  <div className="card-content">
                    Modern CSS features enable more maintainable and performant typography systems.
                    <span className="highlight"> Highlighted important information </span>
                    stands out naturally.
                  </div>
                </div>
                
                <div className="container-responsive-text p-4 border rounded">
                  <h3 className="text-h5 mb-2">Container Query Typography</h3>
                  <p>This text responds to its container size, not viewport size, 
                     enabling truly modular typography components.</p>
                </div>
                
                <div className="p-4 border rounded thai-english-kerning">
                  <h3 className="text-h5 mb-2">Advanced Kerning</h3>
                  <p className="thai-with-tones">
                    ข้อมูลผู้ป่วย: นายสมชาย ใจดี อายุ 45 ปี
                    <br />
                    Lab results: HbA1c 7.2%, FBS 156 mg/dL
                  </p>
                </div>
              </div>
            </Card>
          </div>
        </TypographyVariablesProvider>
      </ProgressiveFontLoader>
    </TypographyContext.Provider>
  )
}