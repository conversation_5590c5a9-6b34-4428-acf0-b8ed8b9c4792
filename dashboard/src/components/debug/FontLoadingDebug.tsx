/**
 * Font Loading Debug Component
 * Real-time monitoring of FC Iconic font loading status
 */

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'

interface FontStatus {
  name: string
  loaded: boolean
  available: boolean
  loadTime?: number
  error?: string
}

export const FontLoadingDebug: React.FC = () => {
  const [fontStatuses, setFontStatuses] = useState<FontStatus[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkFonts = async () => {
      const fonts = [
        { name: 'FC Iconic Regular', spec: '400 16px FC Iconic' },
        { name: 'FC Iconic Bold', spec: '700 16px FC Iconic' },
        { name: 'FC Iconic Italic', spec: 'italic 400 16px FC Iconic' },
        { name: 'FC Iconic Bold Italic', spec: 'italic 700 16px FC Iconic' },
      ]

      const statuses: FontStatus[] = []

      for (const font of fonts) {
        const startTime = performance.now()
        try {
          // Check if font is immediately available
          const available = document.fonts.check(font.spec)
          
          if (available) {
            statuses.push({
              name: font.name,
              loaded: true,
              available: true,
              loadTime: 0
            })
          } else {
            // Try to load the font
            await document.fonts.load(font.spec)
            const loadTime = performance.now() - startTime
            const nowAvailable = document.fonts.check(font.spec)
            
            statuses.push({
              name: font.name,
              loaded: nowAvailable,
              available: nowAvailable,
              loadTime
            })
          }
        } catch (error) {
          statuses.push({
            name: font.name,
            loaded: false,
            available: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      setFontStatuses(statuses)
      setIsLoading(false)
    }

    checkFonts()

    // Listen for font loading events
    const handleFontLoad = () => {
      checkFonts()
    }

    document.fonts.addEventListener('loadingdone', handleFontLoad)
    document.fonts.addEventListener('loadingerror', handleFontLoad)

    return () => {
      document.fonts.removeEventListener('loadingdone', handleFontLoad)
      document.fonts.removeEventListener('loadingerror', handleFontLoad)
    }
  }, [])

  const getStatusColor = (status: FontStatus) => {
    if (status.loaded) return 'text-green-600'
    if (status.error) return 'text-red-600'
    return 'text-yellow-600'
  }

  const getStatusIcon = (status: FontStatus) => {
    if (status.loaded) return '✅'
    if (status.error) return '❌'
    return '⏳'
  }

  return (
    <Card className="p-6">
      <h3 className="text-lg font-bold mb-4">FC Iconic Font Loading Status</h3>
      
      {isLoading ? (
        <div className="text-center py-4">
          <div className="animate-spin inline-block w-6 h-6 border-2 border-primary-500 border-t-transparent rounded-full"></div>
          <p className="mt-2 text-sm text-neutral-600">Checking font loading status...</p>
        </div>
      ) : (
        <div className="space-y-3">
          {fontStatuses.map((status) => (
            <div key={status.name} className="flex items-center justify-between p-3 border border-neutral-200 rounded-lg">
              <div className="flex items-center gap-3">
                <span className="text-lg">{getStatusIcon(status)}</span>
                <div>
                  <p className={`font-medium ${getStatusColor(status)}`}>
                    {status.name}
                  </p>
                  {status.error && (
                    <p className="text-xs text-red-500 mt-1">{status.error}</p>
                  )}
                </div>
              </div>
              <div className="text-right text-sm">
                <p className={getStatusColor(status)}>
                  {status.loaded ? 'Loaded' : status.error ? 'Failed' : 'Loading'}
                </p>
                {status.loadTime !== undefined && (
                  <p className="text-xs text-neutral-500">
                    {status.loadTime === 0 ? 'Cached' : `${status.loadTime.toFixed(0)}ms`}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-6 p-4 bg-neutral-50 rounded-lg">
        <h4 className="font-medium mb-2">Font Family Test</h4>
        <div className="space-y-2 text-sm">
          <p style={{ fontFamily: 'FC Iconic, sans-serif', fontSize: '32px' }}>
            FC Iconic Test Text - ทดสอบฟอนต์ไทย
          </p>
          <p className="text-xs text-neutral-600">
            If you see FC Iconic font above, it's working correctly.
            If you see a fallback font, there's a loading issue.
          </p>
        </div>
      </div>

      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium mb-2">Browser Font API Status</h4>
        <div className="text-sm space-y-1">
          <p>Document fonts ready: {document.fonts.ready ? '✅' : '❌'}</p>
          <p>Font loading API supported: {document.fonts ? '✅' : '❌'}</p>
          <p>Total fonts loaded: {document.fonts.size}</p>
        </div>
      </div>
    </Card>
  )
}

export default FontLoadingDebug
