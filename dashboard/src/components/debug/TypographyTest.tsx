/**
 * Typography Test Component
 * Used to verify CSS custom property hot-reload functionality
 */

import React from 'react'
import { FontLoadingDebug } from './FontLoadingDebug'

export const TypographyTest: React.FC = () => {
  return (
    <div className="p-8 space-y-6 bg-white rounded-lg shadow-card">
      <h2 className="text-2xl font-bold text-primary-700 mb-4">
        Typography Hot-Reload Test
      </h2>
      
      <div className="space-y-4">
        <div className="p-4 border border-neutral-200 rounded-lg">
          <h3 className="text-lg font-bold mb-2">TailwindCSS Utility Classes</h3>
          <p className="text-xs mb-1">text-xs: This should change when --text-xs changes</p>
          <p className="text-sm mb-1">text-sm: This should change when --text-sm changes</p>
          <p className="text-base mb-1">text-base: This should change when --text-base changes</p>
          <p className="text-lg mb-1">text-lg: This should change when --text-lg changes</p>
          <p className="text-xl mb-1">text-xl: This should change when --text-xl changes</p>
        </div>
        
        <div className="p-4 border border-neutral-200 rounded-lg">
          <h3 className="text-lg font-bold mb-2">Custom CSS Classes</h3>
          <p className="text-ultra-hero mb-2">Ultra Hero Text</p>
          <p className="text-hero mb-2">Hero Text</p>
          <p className="text-h1 mb-2">H1 Text</p>
          <p className="text-body mb-2">Body Text</p>
          <p className="text-caption mb-2">Caption Text</p>
        </div>
        
        <div className="p-4 border border-neutral-200 rounded-lg">
          <h3 className="text-lg font-bold mb-2">CSS Custom Property Values</h3>
          <div className="grid grid-cols-2 gap-4 text-sm font-mono">
            <div>--text-xs: <span style={{ fontSize: 'var(--text-xs)' }}>12px</span></div>
            <div>--text-sm: <span style={{ fontSize: 'var(--text-sm)' }}>14px</span></div>
            <div>--text-base: <span style={{ fontSize: 'var(--text-base)' }}>16px</span></div>
            <div>--text-lg: <span style={{ fontSize: 'var(--text-lg)' }}>18px</span></div>
            <div>--text-xl: <span style={{ fontSize: 'var(--text-xl)' }}>20px</span></div>
            <div>--text-2xl: <span style={{ fontSize: 'var(--text-2xl)' }}>24px</span></div>
          </div>
        </div>
      </div>
      
      <div className="mt-6 p-4 bg-primary-50 rounded-lg">
        <h4 className="text-base font-bold text-primary-700 mb-2">Test Instructions:</h4>
        <ol className="text-sm text-primary-600 space-y-1">
          <li>1. Change <code className="bg-white px-1 rounded">--text-base</code> from 16px to 32px in globals.css</li>
          <li>2. Save the file and observe if both TailwindCSS and custom classes update</li>
          <li>3. The "text-base" utility class should now be 32px</li>
          <li>4. The CSS custom property display should show the new value</li>
        </ol>
      </div>

      {/* Font Loading Debug Component */}
      <div className="mt-6">
        <FontLoadingDebug />
      </div>
    </div>
  )
}

export default TypographyTest
