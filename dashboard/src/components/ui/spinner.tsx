import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"

const spinnerVariants = cva(
  "animate-spin",
  {
    variants: {
      size: {
        xs: "h-3 w-3",
        sm: "h-4 w-4", 
        default: "h-6 w-6",
        lg: "h-8 w-8",
        xl: "h-12 w-12"
      },
      variant: {
        default: "text-neutral-600",
        primary: "text-primary-600",
        white: "text-white",
        medical: "text-primary-700"
      }
    },
    defaultVariants: {
      size: "default",
      variant: "default"
    }
  }
)

export interface SpinnerProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'children'>,
    VariantProps<typeof spinnerVariants> {
  label?: string
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size, variant, label, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("flex items-center justify-center", className)}
        role="status"
        aria-label={label || "Loading"}
        {...props}
      >
        <Loader2 className={spinnerVariants({ size, variant })} />
        {label && (
          <span className="ml-2 text-body-sm text-neutral-600">{label}</span>
        )}
      </div>
    )
  }
)
Spinner.displayName = "Spinner"

// Loading Skeleton Component
export interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string
}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "animate-pulse rounded-md bg-neutral-100",
          className
        )}
        {...props}
      />
    )
  }
)
Skeleton.displayName = "Skeleton"

// Medical Loading Component with ChromoForge branding
export interface MedicalLoadingProps {
  message?: string
  showLogo?: boolean
  fullscreen?: boolean
}

const MedicalLoading = React.forwardRef<HTMLDivElement, MedicalLoadingProps>(
  ({ message = "Processing medical data...", showLogo = true, fullscreen = false }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col items-center justify-center space-y-4",
          fullscreen && "fixed inset-0 bg-white/80 backdrop-blur-sm z-50"
        )}
      >
        {showLogo && (
          <div className="flex items-center space-x-3 mb-4">
            <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-primary-700 to-primary-500 flex items-center justify-center">
              <span className="logo-header text-white">C</span>
            </div>
            <span className="logo-header text-primary-700">
              CHROMO ◆ FORGE
            </span>
          </div>
        )}
        
        <Spinner size="lg" variant="medical" />
        
        <div className="text-center space-y-2">
          <p className="text-body-lg font-bold text-neutral-800">{message}</p>
          <p className="text-body-sm text-neutral-600">
            Please wait while we process your request
          </p>
        </div>
        
        {/* Medical progress indicator */}
        <div className="w-64 h-1 bg-neutral-200 rounded-full overflow-hidden">
          <div className="h-full bg-gradient-to-r from-primary-600 to-primary-400 rounded-full animate-pulse" />
        </div>
      </div>
    )
  }
)
MedicalLoading.displayName = "MedicalLoading"

// Page Loading Component
export interface PageLoadingProps {
  title?: string
  description?: string
}

const PageLoading = React.forwardRef<HTMLDivElement, PageLoadingProps>(
  ({ title = "Loading", description }, ref) => {
    return (
      <div ref={ref} className="flex flex-col items-center justify-center min-h-96 space-y-6">
        <div className="space-y-4 text-center">
          <Skeleton className="h-8 w-48 mx-auto" />
          {description && <Skeleton className="h-4 w-64 mx-auto" />}
        </div>
        
        <div className="space-y-3 w-full max-w-md">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
        
        <Spinner size="lg" variant="medical" />
      </div>
    )
  }
)
PageLoading.displayName = "PageLoading"

export { 
  Spinner, 
  Skeleton, 
  MedicalLoading, 
  PageLoading, 
  spinnerVariants 
}