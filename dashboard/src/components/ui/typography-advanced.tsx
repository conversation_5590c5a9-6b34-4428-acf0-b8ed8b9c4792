/**
 * ChromoForge Advanced Typography System
 * Innovative enhancements for medical interface typography
 * Pushing boundaries with dynamic, context-aware, and performance-optimized solutions
 */

import * as React from "react"
import { cn } from "@/lib/utils"

// Advanced Typography Context for Dynamic Behavior
interface TypographyContextValue {
  density: 'compact' | 'normal' | 'comfortable'
  contentComplexity: 'simple' | 'moderate' | 'complex'
  viewingConditions: 'optimal' | 'bright' | 'dim'
  userPreferences: {
    readingSpeed: 'slow' | 'normal' | 'fast'
    focusMode: boolean
    highContrast: boolean
  }
}

const TypographyContext = React.createContext<TypographyContextValue>({
  density: 'normal',
  contentComplexity: 'moderate',
  viewingConditions: 'optimal',
  userPreferences: {
    readingSpeed: 'normal',
    focusMode: false,
    highContrast: false
  }
})

// Dynamic Font Size Calculator
export const useDynamicFontSize = (
  baseSizePx: number,
  contentType: 'medical-term' | 'patient-data' | 'instruction' | 'general'
) => {
  const context = React.useContext(TypographyContext)
  
  return React.useMemo(() => {
    let adjustedSize = baseSizePx
    
    // Content complexity adjustments
    if (context.contentComplexity === 'complex' && contentType === 'medical-term') {
      adjustedSize *= 1.15 // Increase size for complex medical terms
    }
    
    // Density adjustments
    const densityMultipliers = {
      compact: 0.9,
      normal: 1,
      comfortable: 1.1
    }
    adjustedSize *= densityMultipliers[context.density]
    
    // Viewing condition adjustments
    if (context.viewingConditions === 'dim') {
      adjustedSize *= 1.08 // Slight increase for dim conditions
    } else if (context.viewingConditions === 'bright') {
      adjustedSize *= 1.05 // Compensate for glare
    }
    
    // Reading speed adjustments
    if (context.userPreferences.readingSpeed === 'slow') {
      adjustedSize *= 1.06
    }
    
    return Math.round(adjustedSize)
  }, [baseSizePx, contentType, context])
}

// Intelligent Thai-English Flow Optimizer
export const ThaiEnglishOptimizer: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className }) => {
  const ref = React.useRef<HTMLDivElement>(null)
  
  React.useEffect(() => {
    if (!ref.current) return
    
    // Analyze text content for Thai-English boundaries
    const textNodes = Array.from(ref.current.childNodes).filter(
      node => node.nodeType === Node.TEXT_NODE
    )
    
    textNodes.forEach(node => {
      const text = node.textContent || ''
      // Pattern to detect Thai-English transitions
      const pattern = /([ก-๙]+)\s*([a-zA-Z]+)|([a-zA-Z]+)\s*([ก-๙]+)/g
      
      if (pattern.test(text)) {
        // Apply micro-spacing adjustments at language boundaries
        const wrapper = document.createElement('span')
        wrapper.innerHTML = text.replace(pattern, (match, thai1, eng1, eng2, thai2) => {
          if (thai1 && eng1) {
            return `${thai1}<span class="lang-boundary-space"></span>${eng1}`
          } else if (eng2 && thai2) {
            return `${eng2}<span class="lang-boundary-space"></span>${thai2}`
          }
          return match
        })
        node.parentNode?.replaceChild(wrapper, node)
      }
    })
  }, [children])
  
  return (
    <div ref={ref} className={cn("thai-english-optimized", className)}>
      {children}
    </div>
  )
}

// Context-Aware Medical Typography Component
export const MedicalTypography: React.FC<{
  variant: 'diagnosis' | 'prescription' | 'lab-result' | 'patient-info'
  priority: 'critical' | 'important' | 'normal'
  children: React.ReactNode
  className?: string
}> = ({ variant, priority, children, className }) => {
  const baseSizes = {
    diagnosis: 18,
    prescription: 16,
    'lab-result': 14,
    'patient-info': 16
  }
  
  const dynamicSize = useDynamicFontSize(baseSizes[variant], 'medical-term')
  
  const priorityClasses = {
    critical: 'text-error font-bold animate-pulse-subtle',
    important: 'text-warning font-bold',
    normal: 'text-primary'
  }
  
  return (
    <span 
      className={cn(
        "medical-typography",
        priorityClasses[priority],
        className
      )}
      style={{ fontSize: `${dynamicSize}px` }}
    >
      {children}
    </span>
  )
}

// Enhanced Progressive Font Loading with Performance Monitoring
export const ProgressiveFontLoader: React.FC<{
  children: React.ReactNode
  onLoadingComplete?: (metrics: FontLoadingMetrics) => void
}> = ({ children, onLoadingComplete }) => {
  const [fontStatus, setFontStatus] = React.useState<'loading' | 'loaded' | 'fallback'>('loading')
  const [loadingProgress, setLoadingProgress] = React.useState(0)
  const loadStartTime = React.useRef<number>(0)
  
  React.useEffect(() => {
    loadStartTime.current = performance.now()
    
    // Check if fonts are already loaded
    if (document.fonts.check('1rem FC Iconic')) {
      setFontStatus('loaded')
      setLoadingProgress(100)
      document.documentElement.classList.add('fc-iconic-loaded')
      onLoadingComplete?.({
        loadTime: 0,
        fromCache: true,
        status: 'loaded'
      })
      return
    }
    
    // Enhanced progressive loading with medical terminology priority
    const loadFonts = async () => {
      try {
        // Stage 1: Critical medical characters (30%)
        setLoadingProgress(10)
        await document.fonts.load('400 1rem FC Iconic', 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789')
        setLoadingProgress(30)
        
        // Stage 2: Thai medical terms (60%)
        await document.fonts.load('400 1rem FC Iconic', 'ผู้ป่วย แพทย์ โรงพยาบาล ยา การรักษา อาการ โรค')
        setLoadingProgress(60)
        
        // Stage 3: Full character set (80%)
        await document.fonts.load('400 1rem FC Iconic', 'กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรลวศษสหฬอฮ')
        setLoadingProgress(80)
        
        // Stage 4: Bold weights (100%)
        await document.fonts.load('700 1rem FC Iconic')
        setLoadingProgress(100)
        
        const loadTime = performance.now() - loadStartTime.current
        setFontStatus('loaded')
        document.documentElement.classList.add('fc-iconic-loaded')
        
        onLoadingComplete?.({
          loadTime,
          fromCache: false,
          status: 'loaded'
        })
      } catch (error) {
        console.warn('Font loading failed, using fallback', error)
        setFontStatus('fallback')
        const loadTime = performance.now() - loadStartTime.current
        
        onLoadingComplete?.({
          loadTime,
          fromCache: false,
          status: 'fallback',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    loadFonts()
  }, [onLoadingComplete])
  
  return (
    <div className={cn(
      "font-loader-wrapper transition-all duration-300",
      fontStatus === 'loading' && "font-loading",
      fontStatus === 'fallback' && "font-fallback"
    )}>
      {fontStatus === 'loading' && (
        <div 
          className="font-loading-progress"
          style={{ '--progress': `${loadingProgress}%` } as React.CSSProperties}
          aria-label={`Font loading: ${loadingProgress}%`}
        />
      )}
      {children}
    </div>
  )
}

// Adaptive Contrast Typography
export const AdaptiveContrastText: React.FC<{
  children: React.ReactNode
  className?: string
  baseContrast?: number
}> = ({ children, className, baseContrast = 7 }) => {
  const [contrast, setContrast] = React.useState(baseContrast)
  const context = React.useContext(TypographyContext)
  
  React.useEffect(() => {
    let adjustedContrast = baseContrast
    
    // Adjust for viewing conditions
    if (context.viewingConditions === 'bright') {
      adjustedContrast = Math.min(adjustedContrast * 1.3, 21) // WCAG AAA max
    } else if (context.viewingConditions === 'dim') {
      adjustedContrast = Math.max(adjustedContrast * 0.9, 4.5) // WCAG AA min
    }
    
    // High contrast mode override
    if (context.userPreferences.highContrast) {
      adjustedContrast = 21
    }
    
    setContrast(adjustedContrast)
  }, [baseContrast, context])
  
  // Calculate color based on contrast ratio
  const textColor = React.useMemo(() => {
    // Simplified contrast calculation for demo
    const lightness = 100 - (contrast * 4.5)
    return `hsl(174, 100%, ${Math.max(0, Math.min(50, lightness))}%)`
  }, [contrast])
  
  return (
    <span 
      className={cn("adaptive-contrast", className)}
      style={{ color: textColor }}
      data-contrast={contrast}
    >
      {children}
    </span>
  )
}

// Enhanced Smart Focus Management with Medical Form Support
export const SmartFocusableText: React.FC<{
  children: React.ReactNode
  focusGroup: string
  priority?: number
  className?: string
  medicalContext?: 'critical' | 'important' | 'normal'
  skipLink?: boolean
}> = ({ children, focusGroup, priority = 0, className, medicalContext = 'normal', skipLink = false }) => {
  const ref = React.useRef<HTMLDivElement>(null)
  const [isFocused, setIsFocused] = React.useState(false)
  const [isVisible, setIsVisible] = React.useState(false)
  
  React.useEffect(() => {
    const element = ref.current
    if (!element) return
    
    // Enhanced focus management for medical professionals
    const handleFocusIn = (e: FocusEvent) => {
      setIsFocused(true)
      setIsVisible(true)
      
      // Enhanced screen reader support
      const urgencyLevel = medicalContext === 'critical' ? 'assertive' : 
                          priority > 5 ? 'assertive' : 'polite'
      element.setAttribute('aria-live', urgencyLevel)
      
      // Smooth focus transition for professional feel
      element.style.transition = 'all 150ms cubic-bezier(0.25, 0.46, 0.45, 0.94)'
      
      // Add medical context to focus announcement
      if (medicalContext === 'critical') {
        element.setAttribute('aria-describedby', 'critical-medical-info')
      }
    }
    
    const handleFocusOut = (e: FocusEvent) => {
      // Only lose focus if not moving to a child element
      if (!element.contains(e.relatedTarget as Node)) {
        setIsFocused(false)
        element.removeAttribute('aria-live')
        element.removeAttribute('aria-describedby')
        
        // Fade out for skip links
        if (skipLink) {
          setTimeout(() => setIsVisible(false), 150)
        }
      }
    }
    
    // Keyboard navigation enhancement
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && medicalContext === 'critical') {
        // Allow escape from critical focus traps
        (e.target as HTMLElement).blur()
      }
    }
    
    element.addEventListener('focusin', handleFocusIn)
    element.addEventListener('focusout', handleFocusOut)
    element.addEventListener('keydown', handleKeyDown)
    
    return () => {
      element.removeEventListener('focusin', handleFocusIn)
      element.removeEventListener('focusout', handleFocusOut)
      element.removeEventListener('keydown', handleKeyDown)
    }
  }, [priority, medicalContext, skipLink])
  
  return (
    <>
      {medicalContext === 'critical' && (
        <div id="critical-medical-info" className="sr-only">
          Critical medical information - please review carefully
        </div>
      )}
      <div
        ref={ref}
        tabIndex={skipLink ? -1 : 0}
        className={cn(
          "smart-focusable transition-focus",
          isFocused && "focused",
          `focus-priority-${priority}`,
          `medical-${medicalContext}`,
          skipLink && !isVisible && "sr-only",
          skipLink && isVisible && "sr-only-focusable",
          "touch-target", // Enhanced mobile support
          className
        )}
        data-focus-group={focusGroup}
        data-medical-context={medicalContext}
        role="group"
        aria-label={`${medicalContext === 'critical' ? 'Critical ' : ''}Focus group: ${focusGroup}`}
      >
        {children}
      </div>
    </>
  )
}

// Variable Font Weight Animation Hook
export const useVariableFontAnimation = (
  baseWeight: number,
  animationType: 'pulse' | 'breathe' | 'emphasize'
) => {
  const [weight, setWeight] = React.useState(baseWeight)
  
  React.useEffect(() => {
    let animationFrame: number
    let startTime: number
    
    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp
      const progress = timestamp - startTime
      
      let newWeight = baseWeight
      
      switch (animationType) {
        case 'pulse':
          // Quick pulse for alerts
          newWeight = baseWeight + Math.sin(progress * 0.006) * 100
          break
        case 'breathe':
          // Slow breathing for ambient effects
          newWeight = baseWeight + Math.sin(progress * 0.001) * 50
          break
        case 'emphasize':
          // One-time emphasis animation
          const emphasisProgress = Math.min(progress / 1000, 1)
          newWeight = baseWeight + (Math.sin(emphasisProgress * Math.PI) * 200)
          if (emphasisProgress >= 1) return
          break
      }
      
      setWeight(Math.max(300, Math.min(700, newWeight)))
      animationFrame = requestAnimationFrame(animate)
    }
    
    animationFrame = requestAnimationFrame(animate)
    
    return () => cancelAnimationFrame(animationFrame)
  }, [baseWeight, animationType])
  
  return weight
}

// Medical Terminology Subsetting Component
export const MedicalTermSubsetter: React.FC<{
  terms: string[]
  onSubsetReady?: (subset: string) => void
}> = ({ terms, onSubsetReady }) => {
  React.useEffect(() => {
    const uniqueChars = new Set<string>()
    
    // Extract unique characters from medical terms
    terms.forEach(term => {
      Array.from(term).forEach(char => uniqueChars.add(char))
    })
    
    // Generate unicode range for subsetting
    const ranges: string[] = []
    const sortedChars = Array.from(uniqueChars).sort((a, b) => 
      a.charCodeAt(0) - b.charCodeAt(0)
    )
    
    let rangeStart = sortedChars[0]?.charCodeAt(0)
    let rangeEnd = rangeStart
    
    sortedChars.forEach((char, i) => {
      const code = char.charCodeAt(0)
      if (code === rangeEnd + 1) {
        rangeEnd = code
      } else if (i > 0) {
        ranges.push(`U+${rangeStart.toString(16).toUpperCase().padStart(4, '0')}-${rangeEnd.toString(16).toUpperCase().padStart(4, '0')}`)
        rangeStart = rangeEnd = code
      }
    })
    
    if (rangeStart && rangeEnd) {
      ranges.push(`U+${rangeStart.toString(16).toUpperCase().padStart(4, '0')}-${rangeEnd.toString(16).toUpperCase().padStart(4, '0')}`)
    }
    
    const unicodeRange = ranges.join(', ')
    onSubsetReady?.(unicodeRange)
  }, [terms, onSubsetReady])
  
  return null
}

// Typography Performance Monitor
export const TypographyPerformanceMonitor: React.FC<{
  onMetrics?: (metrics: TypographyMetrics) => void
}> = ({ onMetrics }) => {
  React.useEffect(() => {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      
      entries.forEach(entry => {
        if (entry.name.includes('font')) {
          const metrics: TypographyMetrics = {
            fontLoadTime: entry.duration,
            renderTime: performance.now() - entry.startTime,
            fontFamily: entry.name,
            timestamp: Date.now()
          }
          onMetrics?.(metrics)
        }
      })
    })
    
    observer.observe({ entryTypes: ['resource', 'paint'] })
    
    return () => observer.disconnect()
  }, [onMetrics])
  
  return null
}

// Enhanced CSS Variables for Dynamic Typography
export const TypographyVariablesProvider: React.FC<{
  children: React.ReactNode
  config?: Partial<TypographyConfig>
}> = ({ children, config }) => {
  const ref = React.useRef<HTMLDivElement>(null)
  
  React.useEffect(() => {
    if (!ref.current) return
    
    const variables = {
      '--dynamic-base-size': `${config?.baseSize || 16}px`,
      '--dynamic-scale-ratio': config?.scaleRatio || 1.25,
      '--dynamic-line-height': config?.lineHeight || 1.6,
      '--dynamic-letter-spacing': `${config?.letterSpacing || 0}em`,
      '--thai-latin-spacing': `${config?.thaiLatinSpacing || 0.1}em`,
      '--medical-term-weight': config?.medicalTermWeight || 500,
      '--focus-scale': config?.focusScale || 1.05,
      '--contrast-adjust': config?.contrastAdjust || 1
    }
    
    Object.entries(variables).forEach(([key, value]) => {
      ref.current?.style.setProperty(key, String(value))
    })
  }, [config])
  
  return (
    <div ref={ref} className="typography-variables-provider">
      {children}
    </div>
  )
}

// Enhanced Types for Medical Typography System
interface TypographyMetrics {
  fontLoadTime: number
  renderTime: number
  fontFamily: string
  timestamp: number
}

interface FontLoadingMetrics {
  loadTime: number
  fromCache: boolean
  status: 'loaded' | 'fallback'
  error?: string
}

interface TypographyConfig {
  baseSize: number
  scaleRatio: number
  lineHeight: number
  letterSpacing: number
  thaiLatinSpacing: number
  medicalTermWeight: number
  focusScale: number
  contrastAdjust: number
}

interface MedicalTypographyProps {
  variant: 'diagnosis' | 'prescription' | 'lab-result' | 'patient-info'
  priority: 'critical' | 'important' | 'normal'
  children: React.ReactNode
  className?: string
  interactive?: boolean
  printOptimized?: boolean
}

export { TypographyContext }