/**
 * ChromoForge Typography Showcase Component
 * Demonstrates the complete FC Iconic typography system implementation
 * Based on ChromoForge Design System specifications
 */

import * as React from "react"
import { cn } from "@/lib/utils"

// Typography Showcase Component
export interface TypographyShowcaseProps {
  className?: string
}

const TypographyShowcase = React.forwardRef<HTMLDivElement, TypographyShowcaseProps>(
  ({ className }, ref) => {
    return (
      <div ref={ref} className={cn("space-y-12 p-8", className)}>
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-h1">ChromoForge Typography System</h1>
          <p className="text-lead text-neutral-600">
            FC Iconic font implementation with comprehensive hierarchy and Thai language support
          </p>
        </div>

        {/* Logo Variants */}
        <section className="space-y-6">
          <h2 className="text-h2">Logo System</h2>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4 p-6 border border-neutral-200 rounded-xl">
              <h3 className="text-h4">Logo Variants</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="h-16 w-16 rounded-lg bg-gradient-to-br from-primary-700 to-primary-500 flex items-center justify-center shadow-card">
                    <span className="logo-hero text-white">C</span>
                  </div>
                  <span className="logo-hero text-primary-700">CHROMO ◆ FORGE</span>
                </div>
                <p className="text-caption">Hero Logo - Maximum Impact</p>
              </div>
            </div>
            
            <div className="space-y-4 p-6 border border-neutral-200 rounded-xl">
              <h3 className="text-h4">Header Logo</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-primary-700 to-primary-500 flex items-center justify-center shadow-card">
                    <span className="logo-compact text-white">C</span>
                  </div>
                  <span className="logo-header text-primary-700">CHROMO ◆ FORGE</span>
                </div>
                <p className="text-caption">Navigation Header</p>
              </div>
            </div>
          </div>
        </section>

        {/* Display Typography */}
        <section className="space-y-6">
          <h2 className="text-h2">Display Typography</h2>
          <div className="space-y-8">
            <div>
              <h1 className="text-ultra-hero text-center mb-4">Ultra Hero</h1>
              <p className="text-caption text-center">72px, Bold, Uppercase - Maximum Brand Impact</p>
            </div>
            <div>
              <h1 className="text-hero text-center mb-4">Hero Display</h1>
              <p className="text-caption text-center">56px, Bold - Landing Pages</p>
            </div>
            <div>
              <h1 className="text-display-lg text-center mb-4">Display Large</h1>
              <p className="text-caption text-center">48px, Bold - Section Heroes</p>
            </div>
          </div>
        </section>

        {/* Heading Hierarchy */}
        <section className="space-y-6">
          <h2 className="text-h2">Heading Hierarchy</h2>
          <div className="space-y-6">
            <div>
              <h1 className="text-h1 mb-2">H1 - Page Titles</h1>
              <p className="text-caption">36px, Bold - Maximum hierarchy</p>
            </div>
            <div>
              <h2 className="text-h2 mb-2">H2 - Major Sections</h2>
              <p className="text-caption">30px, Bold - Section divisions</p>
            </div>
            <div>
              <h3 className="text-h3 mb-2">H3 - Subsections</h3>
              <p className="text-caption">24px, Bold - Content grouping</p>
            </div>
            <div>
              <h4 className="text-h4 mb-2">H4 - Component Titles</h4>
              <p className="text-caption">20px, Regular, Uppercase - Cards and panels</p>
            </div>
            <div>
              <h5 className="text-h5 mb-2">H5 - Card Headers</h5>
              <p className="text-caption">18px, Regular, Uppercase - Small sections</p>
            </div>
            <div>
              <h6 className="text-h6 mb-2">H6 - Small Labels</h6>
              <p className="text-caption">16px, Regular, Uppercase - Form sections</p>
            </div>
          </div>
        </section>

        {/* Body Content */}
        <section className="space-y-6">
          <h2 className="text-h2">Body Typography</h2>
          <div className="space-y-6">
            <div>
              <p className="text-lead mb-2">
                Lead Paragraph - Size-based hierarchy for introductory content that needs more presence than standard body text.
              </p>
              <p className="text-caption">20px, Regular - Lead content</p>
            </div>
            <div>
              <p className="text-body-lg mb-2">
                Body Large - Comfortable reading size for content that requires enhanced readability. Perfect for medical documentation and detailed descriptions.
              </p>
              <p className="text-caption">18px, Regular - Enhanced readability</p>
            </div>
            <div>
              <p className="text-body mb-2">
                Body Regular - Standard interface text used throughout the application. Optimized for FC Iconic's character width and medical content readability. This is the primary text style for most content.
              </p>
              <p className="text-caption">16px, Regular - Standard interface text</p>
            </div>
            <div>
              <p className="text-body-sm mb-2">
                Body Small - Secondary information, metadata, and supporting details that need to be readable but less prominent than primary content.
              </p>
              <p className="text-caption">14px, Regular - Secondary information</p>
            </div>
            <div>
              <p className="text-caption mb-2">
                Caption Text - Supplementary text for images, form help text, and minor details. Uses italic styling for subtle differentiation.
              </p>
              <p className="text-caption">12px, Regular, Italic - Supplementary text</p>
            </div>
          </div>
        </section>

        {/* Interactive Elements */}
        <section className="space-y-6">
          <h2 className="text-h2">Interactive Typography</h2>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <h3 className="text-h3">Button Typography</h3>
              <div className="space-y-3">
                <button className="btn-text-lg px-6 py-3 bg-primary-600 text-white rounded-lg">
                  Primary Action
                </button>
                <button className="btn-text-md px-4 py-2 border border-primary-600 text-primary-600 rounded-lg">
                  Secondary Action
                </button>
                <button className="btn-text-sm px-3 py-1 text-primary-600">
                  Text Button
                </button>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-h3">Navigation Typography</h3>
              <div className="space-y-2">
                <div className="nav-text text-neutral-800">Navigation Item</div>
                <div className="nav-text nav-text--active text-primary-600">Active Navigation</div>
              </div>
            </div>
          </div>
        </section>

        {/* Form Elements */}
        <section className="space-y-6">
          <h2 className="text-h2">Form Typography</h2>
          <div className="space-y-4">
            <div>
              <label className="label-text block mb-2">Form Label</label>
              <input 
                type="text" 
                className="input-text w-full px-3 py-2 border border-neutral-300 rounded-lg"
                placeholder="Placeholder text styling"
              />
            </div>
          </div>
        </section>

        {/* Special Cases */}
        <section className="space-y-6">
          <h2 className="text-h2">Special Typography</h2>
          <div className="space-y-6">
            <div>
              <p className="text-body mb-4">
                Text with <span className="text-strong">strong emphasis</span> and 
                <span className="text-emphasis"> subtle emphasis</span> for highlighting 
                important information within content.
              </p>
            </div>
            
            <blockquote className="text-quote border-l-4 border-primary-300 pl-6 py-4 bg-primary-50 rounded-r-lg">
              "Medical data requires the highest standards of accuracy and clarity. 
              FC Iconic delivers both with professional typography designed for healthcare applications."
            </blockquote>
            
            <div>
              <p className="text-body mb-2">Code and technical references:</p>
              <code className="text-code">const medicalData = processOCR(document);</code>
            </div>
          </div>
        </section>

        {/* Thai Language Support */}
        <section className="space-y-6">
          <h2 className="text-h2">Thai Language Support</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-h3 mb-2">ระบบการประมวลผลเอกสารทางการแพทย์</h3>
              <p className="text-body">
                ChromoForge ใช้เทคโนโลยี OCR ขั้นสูงในการประมวลผลเอกสารทางการแพทย์ภาษาไทย 
                ด้วยฟอนต์ FC Iconic ที่รองรับอักษรไทยอย่างสมบูรณ์
              </p>
            </div>
            <div>
              <p className="text-body-sm text-neutral-600">
                แบบอักษร FC Iconic ออกแบบมาเพื่อรองรับการใช้งานกับภาษาไทยและภาษาอังกฤษในระบบเดียวกัน
              </p>
            </div>
          </div>
        </section>

        {/* Accessibility Features */}
        <section className="space-y-6">
          <h2 className="text-h2">Accessibility Features</h2>
          <div className="space-y-4">
            <div className="space-y-2">
              <p className="text-body">Focus ring demonstration:</p>
              <button className="focus-ring px-4 py-2 border border-neutral-300 rounded-lg text-body">
                Focusable Element
              </button>
            </div>
            
            <div className="sr-only-focusable text-center">
              Screen reader accessible content - focus to reveal
            </div>
            
            <p className="text-body-sm text-neutral-600">
              All typography includes proper contrast ratios, screen reader support, and 
              responsive scaling for optimal accessibility.
            </p>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section className="space-y-6">
          <h2 className="text-h2">Usage Guidelines</h2>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4 p-6 border border-green-200 bg-green-50 rounded-xl">
              <h3 className="text-h4 text-green-800">✅ Best Practices</h3>
              <ul className="text-body-sm text-green-700 space-y-2">
                <li>• Use Bold (700) for 20% of interface - headings and emphasis</li>
                <li>• Use Regular (400) for 80% of interface - body content</li>
                <li>• Maintain CHROMO ◆ FORGE logo format</li>
                <li>• Use semantic HTML with typography classes</li>
                <li>• Test with Thai content for full compatibility</li>
              </ul>
            </div>
            
            <div className="space-y-4 p-6 border border-red-200 bg-red-50 rounded-xl">
              <h3 className="text-h4 text-red-800">❌ Avoid</h3>
              <ul className="text-body-sm text-red-700 space-y-2">
                <li>• Don't use font weights other than 400 and 700</li>
                <li>• Don't modify logo spacing or diamond symbol</li>
                <li>• Don't mix typography systems</li>
                <li>• Don't ignore responsive scaling</li>
                <li>• Don't compromise accessibility standards</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    )
  }
)
TypographyShowcase.displayName = "TypographyShowcase"

// Individual Typography Components for easy use
export const H1 = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h1 ref={ref} className={cn("text-h1", className)} {...props} />
))
H1.displayName = "H1"

export const H2 = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h2 ref={ref} className={cn("text-h2", className)} {...props} />
))
H2.displayName = "H2"

export const H3 = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3 ref={ref} className={cn("text-h3", className)} {...props} />
))
H3.displayName = "H3"

export const Lead = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p ref={ref} className={cn("text-lead", className)} {...props} />
))
Lead.displayName = "Lead"

export const Body = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p ref={ref} className={cn("text-body", className)} {...props} />
))
Body.displayName = "Body"

export const Caption = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p ref={ref} className={cn("text-caption", className)} {...props} />
))
Caption.displayName = "Caption"

export const ChromoForgeLogo = React.forwardRef<
  HTMLSpanElement,
  React.HTMLAttributes<HTMLSpanElement> & {
    variant?: 'hero' | 'header' | 'compact' | 'footer'
  }
>(({ className, variant = 'header', ...props }, ref) => (
  <span 
    ref={ref} 
    className={cn(`logo-${variant}`, className)} 
    {...props}
  >
    CHROMO ◆ FORGE
  </span>
))
ChromoForgeLogo.displayName = "ChromoForgeLogo"

export { TypographyShowcase }