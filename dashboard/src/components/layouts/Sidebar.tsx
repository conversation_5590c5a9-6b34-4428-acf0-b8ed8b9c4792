import * as React from "react"
import { 
  BarChart3, 
  ChevronLeft, 
  ChevronRight, 
  Database, 
  FileText, 
  Home, 
  Settings, 
  Shield, 
  Upload, 
  Users, 
  X 
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export interface SidebarProps {
  open: boolean
  collapsed: boolean
  onCollapse: (collapsed: boolean) => void
  onClose: () => void
}

interface NavigationItem {
  id: string
  title: string
  icon: React.ReactNode
  href: string
  badge?: string | number
  active?: boolean
  children?: NavigationItem[]
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: <Home className="h-5 w-5" />,
    href: '/dashboard',
    active: true
  },
  {
    id: 'upload',
    title: 'Upload Documents',
    icon: <Upload className="h-5 w-5" />,
    href: '/upload',
    badge: 'New'
  },
  {
    id: 'records',
    title: 'Medical Records',
    icon: <FileText className="h-5 w-5" />,
    href: '/records',
    badge: 23
  },
  {
    id: 'analytics',
    title: 'Analytics',
    icon: <BarChart3 className="h-5 w-5" />,
    href: '/analytics'
  },
  {
    id: 'data',
    title: 'Data Management',
    icon: <Database className="h-5 w-5" />,
    href: '/data',
    children: [
      {
        id: 'exports',
        title: 'Exports',
        icon: <FileText className="h-4 w-4" />,
        href: '/data/exports'
      },
      {
        id: 'backups',
        title: 'Backups',
        icon: <Shield className="h-4 w-4" />,
        href: '/data/backups'
      }
    ]
  },
  {
    id: 'users',
    title: 'User Management',
    icon: <Users className="h-5 w-5" />,
    href: '/users'
  },
  {
    id: 'settings',
    title: 'Settings',
    icon: <Settings className="h-5 w-5" />,
    href: '/settings'
  }
]

const Sidebar = React.forwardRef<HTMLElement, SidebarProps>(
  ({ open, collapsed, onCollapse, onClose }, ref) => {
    const [expandedItems, setExpandedItems] = React.useState<string[]>([])

    const toggleExpanded = (itemId: string) => {
      setExpandedItems(prev => 
        prev.includes(itemId) 
          ? prev.filter(id => id !== itemId)
          : [...prev, itemId]
      )
    }

    const renderNavigationItem = (item: NavigationItem, depth = 0) => {
      const hasChildren = item.children && item.children.length > 0
      const isExpanded = expandedItems.includes(item.id)
      const paddingLeft = depth === 0 ? 'pl-6' : 'pl-10'

      return (
        <li key={item.id}>
          <div className="space-y-1">
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start gap-3 h-10 font-normal transition-all duration-200",
                paddingLeft,
                collapsed && depth === 0 && "px-3 justify-center",
                item.active && "bg-primary-100 text-primary-700 hover:bg-primary-200",
                !item.active && "text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900"
              )}
              onClick={() => {
                if (hasChildren && !collapsed) {
                  toggleExpanded(item.id)
                } else {
                  // Navigate to href in real app
                  console.log('Navigate to:', item.href)
                }
              }}
            >
              <span className="flex-shrink-0">{item.icon}</span>
              
              {!collapsed && (
                <>
                  <span className="flex-1 text-left truncate">{item.title}</span>
                  
                  {item.badge && (
                    <Badge 
                      variant={typeof item.badge === 'string' ? 'lime' : 'secondary'} 
                      size="sm"
                      className="ml-auto"
                    >
                      {item.badge}
                    </Badge>
                  )}
                  
                  {hasChildren && (
                    <ChevronRight 
                      className={cn(
                        "h-4 w-4 transition-transform",
                        isExpanded && "rotate-90"
                      )} 
                    />
                  )}
                </>
              )}
            </Button>

            {/* Sub-navigation */}
            {hasChildren && isExpanded && !collapsed && (
              <ul className="space-y-1 py-1">
                {item.children?.map(child => renderNavigationItem(child, depth + 1))}
              </ul>
            )}
          </div>
        </li>
      )
    }

    return (
      <>
        <aside
          ref={ref}
          className={cn(
            "fixed left-0 top-14 z-40 h-[calc(100vh-3.5rem)] bg-white/95 backdrop-blur-sm border-r border-neutral-200 transition-all duration-300 ease-in-out shadow-sm",
            open ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
            collapsed ? "w-14" : "w-60"
          )}
        >
          {/* Sidebar Header */}
          <div className={cn(
            "flex items-center justify-between p-3 border-b border-neutral-200",
            collapsed && "justify-center"
          )}>
            {!collapsed && (
              <div>
                <h2 className="text-sm font-semibold text-neutral-800">Navigation</h2>
                <p className="text-xs text-neutral-600">Medical Dashboard</p>
              </div>
            )}
            
            {/* Close button for mobile */}
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="lg:hidden"
              aria-label="Close navigation"
            >
              <X className="h-5 w-5" />
            </Button>
            
            {/* Collapse button for desktop */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onCollapse(!collapsed)}
              className="hidden lg:flex"
              aria-label={collapsed ? "Expand navigation" : "Collapse navigation"}
            >
              {collapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto py-4" aria-label="Main navigation">
            <ul className="space-y-2">
              {navigationItems.map(item => renderNavigationItem(item))}
            </ul>
          </nav>

          {/* Sidebar Footer */}
          {!collapsed && (
            <div className="p-4 border-t border-neutral-200 bg-gradient-to-r from-primary-50 to-primary-100">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-xs font-medium text-primary-700">System Online</span>
                </div>
                <p className="text-xs text-neutral-600">
                  ChromoForge v2.0.0
                </p>
                <p className="text-xs text-neutral-500">
                  Last sync: 2 min ago
                </p>
              </div>
            </div>
          )}
        </aside>
      </>
    )
  }
)
Sidebar.displayName = "Sidebar"

export { Sidebar }