import * as React from "react"
import { Outlet } from "react-router-dom"
import { cn } from "@/lib/utils"
import { Head<PERSON> } from "./Header"
import { Sidebar } from "./Sidebar"

export interface DashboardLayoutProps {
  children?: React.ReactNode
}

const DashboardLayout = React.forwardRef<HTMLDivElement, DashboardLayoutProps>(
  ({ children }, ref) => {
    const [sidebarOpen, setSidebarOpen] = React.useState(true)
    const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false)

    return (
      <div ref={ref} className="min-h-screen bg-neutral-50">
        {/* Skip Navigation Link for Accessibility */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:left-4 focus:top-4 focus:z-50 focus:rounded-md focus:bg-primary-700 focus:px-4 focus:py-2 focus:text-white focus:outline-none focus:ring-2 focus:ring-lime-500"
        >
          Skip to main content
        </a>

        {/* Header */}
        <Header 
          onMenuClick={() => setSidebarOpen(!sidebarOpen)}
          sidebarCollapsed={sidebarCollapsed}
        />

        <div className="flex">
          {/* Sidebar */}
          <Sidebar
            open={sidebarOpen}
            collapsed={sidebarCollapsed}
            onCollapse={setSidebarCollapsed}
            onClose={() => setSidebarOpen(false)}
          />

          {/* Main Content */}
          <main
            id="main-content"
            className={cn(
              "flex-1 transition-all duration-300 ease-in-out",
              sidebarOpen && !sidebarCollapsed && "lg:ml-60",
              sidebarOpen && sidebarCollapsed && "lg:ml-14"
            )}
          >
            <div className="px-3 py-4 sm:px-4 lg:px-6">
              {children || <Outlet />}
            </div>
          </main>
        </div>

        {/* Mobile Sidebar Overlay */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity lg:hidden"
            onClick={() => setSidebarOpen(false)}
            aria-hidden="true"
          />
        )}
      </div>
    )
  }
)
DashboardLayout.displayName = "DashboardLayout"

export { DashboardLayout }