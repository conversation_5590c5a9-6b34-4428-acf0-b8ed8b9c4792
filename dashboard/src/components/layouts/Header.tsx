import * as React from "react"
import { Bell, ChevronDown, LogOut, <PERSON>u, <PERSON><PERSON><PERSON>, User } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"

export interface HeaderProps {
  onMenuClick: () => void
  sidebarCollapsed?: boolean
}

const Header = React.forwardRef<HTMLElement, HeaderProps>(
  ({ onMenuClick, sidebarCollapsed = false }, ref) => {
    const [language, setLanguage] = React.useState<'th' | 'en'>('en')
    const [notifications] = React.useState(3) // Mock notification count

    // Mock user data - in real app this would come from auth context
    const user = {
      name: "<PERSON>. <PERSON><PERSON>",
      email: "<EMAIL>",
      avatar: undefined,
      role: "admin" as const
    }

    return (
      <header
        ref={ref}
        className="sticky top-0 z-50 h-14 border-b border-neutral-200 bg-white/95 backdrop-blur-sm shadow-sm"
      >
        <div className="flex h-full items-center justify-between px-3 sm:px-4 lg:px-6">
          {/* Left Side - Logo & Menu */}
          <div className="flex items-center gap-4">
            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={onMenuClick}
              className="lg:hidden"
              aria-label="Open navigation menu"
            >
              <Menu className="h-5 w-5" />
            </Button>

            {/* ChromoForge Logo - Enhanced FC Iconic Implementation */}
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-primary-700 to-primary-500 flex items-center justify-center shadow-card">
                <span className="logo-compact text-white">C</span>
              </div>
              <div className="hidden sm:block">
                <span className="logo-header text-primary-700">
                  CHROMO ◆ FORGE
                </span>
                <p className="text-caption text-neutral-600 -mt-1">
                  Medical OCR Pipeline
                </p>
              </div>
            </div>
          </div>

          {/* Right Side - Actions */}
          <div className="flex items-center gap-2 sm:gap-4">
            {/* Language Selector */}
            <Select value={language} onValueChange={(value: 'th' | 'en') => setLanguage(value)}>
              <SelectTrigger className="w-20 sm:w-24 h-9">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="th">
                  <span className="flex items-center gap-2">
                    🇹🇭 ไทย
                  </span>
                </SelectItem>
                <SelectItem value="en">
                  <span className="flex items-center gap-2">
                    🇺🇸 EN
                  </span>
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Notifications */}
            <Button 
              variant="ghost" 
              size="icon" 
              className="relative"
              aria-label={`Notifications (${notifications} unread)`}
            >
              <Bell className="h-5 w-5" />
              {notifications > 0 && (
                <Badge 
                  variant="destructive" 
                  size="sm"
                  className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
                >
                  {notifications > 9 ? '9+' : notifications}
                </Badge>
              )}
            </Button>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  className="flex items-center gap-2 px-2 sm:px-3"
                  aria-label="User menu"
                >
                  <Avatar 
                    src={user.avatar} 
                    name={user.name}
                    size="sm"
                    variant="medical"
                  />
                  <div className="hidden md:block text-left">
                    <p className="text-body-sm font-bold text-neutral-900 truncate max-w-32">
                      {user.name}
                    </p>
                    <p className="text-caption text-neutral-600 capitalize">
                      {user.role}
                    </p>
                  </div>
                  <ChevronDown className="h-4 w-4 text-neutral-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 shadow-dropdown">
                <DropdownMenuLabel>
                  <div className="space-y-1">
                    <p className="text-body font-bold">{user.name}</p>
                    <p className="text-caption text-neutral-600">{user.email}</p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  Profile Settings
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  Preferences
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Bell className="mr-2 h-4 w-4" />
                  Notifications
                  {notifications > 0 && (
                    <Badge variant="secondary" size="sm" className="ml-auto">
                      {notifications}
                    </Badge>
                  )}
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem className="text-red-600 focus:text-red-600">
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>
    )
  }
)
Header.displayName = "Header"

export { Header }