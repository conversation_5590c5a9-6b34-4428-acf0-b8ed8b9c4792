/**
 * FC Iconic Font Loading Utility
 * Ensures reliable font loading with proper fallback handling
 */

export interface FontLoadResult {
  success: boolean
  loadTime: number
  fontFamily: string
  error?: string
}

export class FontLoader {
  private static instance: FontLoader
  private loadedFonts = new Set<string>()
  private loadingPromises = new Map<string, Promise<FontLoadResult>>()

  static getInstance(): FontLoader {
    if (!FontLoader.instance) {
      FontLoader.instance = new FontLoader()
    }
    return FontLoader.instance
  }

  /**
   * Load critical FC Iconic fonts with optimized strategy
   */
  async loadCriticalFonts(): Promise<FontLoadResult[]> {
    const criticalFonts = [
      { name: 'FC Iconic Regular', spec: '400 16px FC Iconic' },
      { name: 'FC Iconic Bold', spec: '700 16px FC Iconic' },
    ]

    const results = await Promise.allSettled(
      criticalFonts.map(font => this.loadFont(font.spec, font.name))
    )

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        return {
          success: false,
          loadTime: 0,
          fontFamily: criticalFonts[index].name,
          error: result.reason?.message || 'Unknown error'
        }
      }
    })
  }

  /**
   * Load a specific font with caching and error handling
   */
  async loadFont(fontSpec: string, fontName: string): Promise<FontLoadResult> {
    // Check if already loaded
    if (this.loadedFonts.has(fontSpec)) {
      return {
        success: true,
        loadTime: 0,
        fontFamily: fontName
      }
    }

    // Check if already loading
    if (this.loadingPromises.has(fontSpec)) {
      return this.loadingPromises.get(fontSpec)!
    }

    // Start loading
    const loadingPromise = this.performFontLoad(fontSpec, fontName)
    this.loadingPromises.set(fontSpec, loadingPromise)

    try {
      const result = await loadingPromise
      if (result.success) {
        this.loadedFonts.add(fontSpec)
      }
      return result
    } finally {
      this.loadingPromises.delete(fontSpec)
    }
  }

  private async performFontLoad(fontSpec: string, fontName: string): Promise<FontLoadResult> {
    const startTime = performance.now()

    try {
      // Check if font is immediately available
      if (document.fonts.check(fontSpec)) {
        return {
          success: true,
          loadTime: 0,
          fontFamily: fontName
        }
      }

      // Load the font
      await document.fonts.load(fontSpec)
      const loadTime = performance.now() - startTime

      // Verify it's actually loaded
      const isLoaded = document.fonts.check(fontSpec)
      
      if (isLoaded) {
        // Add CSS class to document for styling
        document.documentElement.classList.add('fc-iconic-loaded')
        
        // Dispatch custom event
        document.dispatchEvent(new CustomEvent('fontLoaded', {
          detail: { fontFamily: fontName, loadTime, fontSpec }
        }))
      }

      return {
        success: isLoaded,
        loadTime,
        fontFamily: fontName,
        error: isLoaded ? undefined : 'Font failed to load properly'
      }
    } catch (error) {
      const loadTime = performance.now() - startTime
      
      // Add fallback class
      document.documentElement.classList.add('font-fallback-active')
      
      return {
        success: false,
        loadTime,
        fontFamily: fontName,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Check if FC Iconic fonts are loaded and working
   */
  isFCIconicLoaded(): boolean {
    return document.fonts.check('400 16px FC Iconic') && 
           document.fonts.check('700 16px FC Iconic')
  }

  /**
   * Get loading status for all fonts
   */
  getLoadingStatus() {
    return {
      loaded: Array.from(this.loadedFonts),
      loading: Array.from(this.loadingPromises.keys()),
      fcIconicReady: this.isFCIconicLoaded(),
      totalFonts: document.fonts.size
    }
  }
}

/**
 * Initialize font loading on app startup
 */
export async function initializeFontLoading(): Promise<void> {
  const fontLoader = FontLoader.getInstance()
  
  try {
    // Wait for document fonts to be ready
    await document.fonts.ready
    
    // Load critical fonts
    const results = await fontLoader.loadCriticalFonts()
    
    // Log results for debugging
    console.log('FC Iconic font loading results:', results)
    
    // Check if any critical fonts failed
    const failedFonts = results.filter(r => !r.success)
    if (failedFonts.length > 0) {
      console.warn('Some FC Iconic fonts failed to load:', failedFonts)
      document.documentElement.classList.add('font-fallback-active')
    }
    
  } catch (error) {
    console.error('Font loading initialization failed:', error)
    document.documentElement.classList.add('font-fallback-active')
  }
}

// Export singleton instance
export const fontLoader = FontLoader.getInstance()
