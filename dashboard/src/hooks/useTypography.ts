/**
 * ChromoForge Enhanced Typography Hooks
 * Frontend-optimized React hooks for medical interface typography
 * Focused on user experience, performance, and accessibility
 */

import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
// Note: typographyEngine would be imported from a separate module in a real implementation
// import { typographyEngine } from '@/lib/typography-engine'

// Hook for responsive typography based on container size
export function useContainerTypography(
  baseSize: number = 16,
  scaleFactor: number = 0.05
) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [fontSize, setFontSize] = useState(baseSize)
  
  useEffect(() => {
    if (!containerRef.current) return
    
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        const width = entry.contentRect.width
        // Scale font size based on container width
        const scaledSize = baseSize + (width - 320) * scaleFactor / 100
        setFontSize(Math.max(14, Math.min(24, scaledSize)))
      }
    })
    
    resizeObserver.observe(containerRef.current)
    
    return () => resizeObserver.disconnect()
  }, [baseSize, scaleFactor])
  
  return { containerRef, fontSize }
}

// Enhanced hook for medical data density with UX optimization
export function useMedicalDataDensity(dataCount: number, userPreference?: 'compact' | 'normal' | 'comfortable') {
  const [density, setDensity] = useState<'compact' | 'normal' | 'comfortable'>('normal')
  const [fontSize, setFontSize] = useState(16)
  const [lineHeight, setLineHeight] = useState(1.6)
  const [spacing, setSpacing] = useState(12)
  const [touchTargetSize, setTouchTargetSize] = useState(44)
  
  useEffect(() => {
    // Respect user preference first, then adapt to data count
    const finalDensity = userPreference || 
      (dataCount > 50 ? 'compact' : dataCount > 20 ? 'normal' : 'comfortable')
    
    setDensity(finalDensity)
    
    switch (finalDensity) {
      case 'compact':
        setFontSize(14)
        setLineHeight(1.35)
        setSpacing(8)
        setTouchTargetSize(40)
        break
      case 'normal':
        setFontSize(16)
        setLineHeight(1.5)
        setSpacing(12)
        setTouchTargetSize(44)
        break
      case 'comfortable':
        setFontSize(18)
        setLineHeight(1.7)
        setSpacing(16)
        setTouchTargetSize(48)
        break
    }
  }, [dataCount, userPreference])
  
  return { 
    density, 
    fontSize, 
    lineHeight, 
    spacing, 
    touchTargetSize,
    cssVariables: {
      '--medical-font-size': `${fontSize}px`,
      '--medical-line-height': lineHeight,
      '--medical-spacing': `${spacing}px`,
      '--medical-touch-target': `${touchTargetSize}px`
    }
  }
}

// Enhanced ambient light detection for medical environments
export function useAmbientLightAdjustment() {
  const [ambientLight, setAmbientLight] = useState(0.5)
  const [contrastMultiplier, setContrastMultiplier] = useState(1)
  const [fontWeight, setFontWeight] = useState(400)
  const [textShadow, setTextShadow] = useState('none')
  const [isAdaptive, setIsAdaptive] = useState(false)
  
  useEffect(() => {
    let sensorAvailable = false
    
    // Check if Ambient Light Sensor API is available
    if ('AmbientLightSensor' in window) {
      try {
        const sensor = new (window as any).AmbientLightSensor({ frequency: 5 })
        sensorAvailable = true
        setIsAdaptive(true)
        
        sensor.addEventListener('reading', () => {
          // Normalize light level (0-2000 lux to 0-1) for medical environments
          const normalized = Math.min(sensor.illuminance / 2000, 1)
          setAmbientLight(normalized)
          
          // Medical environment lighting adaptations
          if (normalized > 0.85) {
            // Very bright (surgical lights, bright fluorescents)
            setContrastMultiplier(1.4)
            setFontWeight(500)
            setTextShadow('0 1px 1px rgba(255, 255, 255, 0.5)')
          } else if (normalized > 0.6) {
            // Standard hospital lighting
            setContrastMultiplier(1.1)
            setFontWeight(400)
            setTextShadow('none')
          } else if (normalized > 0.3) {
            // Dim lighting (night shift, patient rooms)
            setContrastMultiplier(1.0)
            setFontWeight(400)
            setTextShadow('0 0 1px rgba(255, 255, 255, 0.1)')
          } else {
            // Very dim (emergency lighting)
            setContrastMultiplier(0.85)
            setFontWeight(450)
            setTextShadow('0 0 2px rgba(255, 255, 255, 0.2)')
          }
        })
        
        sensor.addEventListener('error', () => {
          console.warn('Ambient Light Sensor error, falling back to time-based adjustment')
          sensorAvailable = false
          setIsAdaptive(false)
        })
        
        sensor.start()
        
        return () => sensor.stop()
      } catch (error) {
        console.log('Ambient Light Sensor not available, using fallback')
        sensorAvailable = false
      }
    }
    
    // Enhanced fallback: Time-based + media query based estimation
    const updateBasedOnEnvironment = () => {
      const hour = new Date().getHours()
      let timeBasedLight = 0.5
      let timeBasedContrast = 1
      
      // Time-based lighting estimation for medical facilities
      if (hour >= 6 && hour < 9) {
        timeBasedLight = 0.6 // Early morning
        timeBasedContrast = 1.05
        setFontWeight(400)
      } else if (hour >= 9 && hour < 17) {
        timeBasedLight = 0.8 // Daytime medical hours
        timeBasedContrast = 1.15
        setFontWeight(400)
      } else if (hour >= 17 && hour < 21) {
        timeBasedLight = 0.5 // Evening shift
        timeBasedContrast = 1.0
        setFontWeight(450)
      } else {
        timeBasedLight = 0.25 // Night shift
        timeBasedContrast = 0.9
        setFontWeight(450)
        setTextShadow('0 0 1px rgba(255, 255, 255, 0.15)')
      }
      
      // Check for prefers-contrast media query
      if (window.matchMedia('(prefers-contrast: high)').matches) {
        timeBasedContrast *= 1.3
        setFontWeight(500)
      } else if (window.matchMedia('(prefers-contrast: low)').matches) {
        timeBasedContrast *= 0.8
      }
      
      if (!sensorAvailable) {
        setAmbientLight(timeBasedLight)
        setContrastMultiplier(timeBasedContrast)
      }
    }
    
    updateBasedOnEnvironment()
    const interval = setInterval(updateBasedOnEnvironment, 300000) // Update every 5 minutes
    
    // Listen for media query changes
    const contrastMediaQuery = window.matchMedia('(prefers-contrast: high)')
    const handleContrastChange = () => updateBasedOnEnvironment()
    contrastMediaQuery.addEventListener('change', handleContrastChange)
    
    return () => {
      clearInterval(interval)
      contrastMediaQuery.removeEventListener('change', handleContrastChange)
    }
  }, [])
  
  return { 
    ambientLight, 
    contrastMultiplier, 
    fontWeight, 
    textShadow,
    isAdaptive,
    cssVariables: {
      '--ambient-contrast': contrastMultiplier,
      '--ambient-font-weight': fontWeight,
      '--ambient-text-shadow': textShadow
    }
  }
}

// Hook for reading speed detection and adaptation
export function useReadingSpeedAdaptation(textContent: string) {
  const [readingSpeed, setReadingSpeed] = useState<'slow' | 'normal' | 'fast'>('normal')
  const [startTime, setStartTime] = useState<number | null>(null)
  const [isReading, setIsReading] = useState(false)
  
  const startReading = useCallback(() => {
    setStartTime(Date.now())
    setIsReading(true)
  }, [])
  
  const endReading = useCallback(() => {
    if (startTime && isReading) {
      const duration = Date.now() - startTime
      const wordCount = textContent.split(/\s+/).length
      const wordsPerMinute = (wordCount / duration) * 60000
      
      if (wordsPerMinute < 150) {
        setReadingSpeed('slow')
      } else if (wordsPerMinute > 250) {
        setReadingSpeed('fast')
      } else {
        setReadingSpeed('normal')
      }
      
      setIsReading(false)
    }
  }, [startTime, isReading, textContent])
  
  const adaptedStyles = useMemo(() => {
    switch (readingSpeed) {
      case 'slow':
        return {
          fontSize: '1.1em',
          lineHeight: 1.8,
          letterSpacing: '0.02em'
        }
      case 'fast':
        return {
          fontSize: '0.95em',
          lineHeight: 1.5,
          letterSpacing: '-0.01em'
        }
      default:
        return {
          fontSize: '1em',
          lineHeight: 1.6,
          letterSpacing: '0'
        }
    }
  }, [readingSpeed])
  
  return { 
    readingSpeed, 
    startReading, 
    endReading, 
    adaptedStyles,
    isReading 
  }
}

// Hook for dynamic font loading based on viewport
export function useViewportFontLoading(
  fonts: Array<{ selector: string; fontFamily: string; unicodeRange?: string }>
) {
  const [loadedFonts, setLoadedFonts] = useState<Set<string>>(new Set())
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement
            const fontFamily = window.getComputedStyle(element).fontFamily
            
            if (!loadedFonts.has(fontFamily)) {
              // Load font if not already loaded
              document.fonts.load(`1rem ${fontFamily}`).then(() => {
                setLoadedFonts(prev => new Set(prev).add(fontFamily))
              })
            }
          }
        })
      },
      { rootMargin: '50px' } // Preload fonts slightly before they come into view
    )
    
    // Observe all elements matching the font selectors
    fonts.forEach(({ selector }) => {
      document.querySelectorAll(selector).forEach(el => {
        observer.observe(el)
      })
    })
    
    return () => observer.disconnect()
  }, [fonts, loadedFonts])
  
  return { loadedFonts }
}

// Hook for Thai text optimization
export function useThaiTextOptimization(text: string) {
  const [optimizedText, setOptimizedText] = useState(text)
  const [metrics, setMetrics] = useState<any>(null)
  
  useEffect(() => {
    const optimizeText = async () => {
      // Use typography engine to shape Thai text
      const shaped = typographyEngine.shapeThaiText(text)
      setOptimizedText(shaped.shaped)
      
      // Calculate optimal line breaks
      const breaks = typographyEngine.calculateOptimalLineBreaks(text, 400, 16)
      
      setMetrics({
        originalLength: text.length,
        shapedLength: shaped.shaped.length,
        adjustments: shaped.adjustments.length,
        lineBreaks: breaks.length
      })
    }
    
    optimizeText()
  }, [text])
  
  return { optimizedText, metrics }
}

// Hook for focus management in complex forms
export function useFocusNavigation(
  groups: Array<{ id: string; priority: number; elements: HTMLElement[] }>
) {
  const [currentGroupIndex, setCurrentGroupIndex] = useState(0)
  const [currentElementIndex, setCurrentElementIndex] = useState(0)
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const sortedGroups = [...groups].sort((a, b) => b.priority - a.priority)
      const currentGroup = sortedGroups[currentGroupIndex]
      
      if (!currentGroup) return
      
      switch (e.key) {
        case 'Tab':
          e.preventDefault()
          if (e.shiftKey) {
            // Move backward
            if (currentElementIndex > 0) {
              setCurrentElementIndex(currentElementIndex - 1)
            } else if (currentGroupIndex > 0) {
              setCurrentGroupIndex(currentGroupIndex - 1)
              setCurrentElementIndex(sortedGroups[currentGroupIndex - 1].elements.length - 1)
            }
          } else {
            // Move forward
            if (currentElementIndex < currentGroup.elements.length - 1) {
              setCurrentElementIndex(currentElementIndex + 1)
            } else if (currentGroupIndex < sortedGroups.length - 1) {
              setCurrentGroupIndex(currentGroupIndex + 1)
              setCurrentElementIndex(0)
            }
          }
          break
          
        case 'ArrowUp':
        case 'ArrowDown':
          if (e.ctrlKey) {
            // Navigate between groups
            e.preventDefault()
            const direction = e.key === 'ArrowUp' ? -1 : 1
            const newIndex = Math.max(0, Math.min(sortedGroups.length - 1, currentGroupIndex + direction))
            setCurrentGroupIndex(newIndex)
            setCurrentElementIndex(0)
          }
          break
      }
    }
    
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [groups, currentGroupIndex, currentElementIndex])
  
  // Focus current element
  useEffect(() => {
    const sortedGroups = [...groups].sort((a, b) => b.priority - a.priority)
    const currentGroup = sortedGroups[currentGroupIndex]
    const currentElement = currentGroup?.elements[currentElementIndex]
    
    if (currentElement) {
      currentElement.focus()
    }
  }, [groups, currentGroupIndex, currentElementIndex])
  
  return {
    currentGroupId: groups[currentGroupIndex]?.id,
    currentElementIndex,
    navigateToGroup: (groupId: string) => {
      const index = groups.findIndex(g => g.id === groupId)
      if (index !== -1) {
        setCurrentGroupIndex(index)
        setCurrentElementIndex(0)
      }
    }
  }
}

// Enhanced typography performance monitoring for medical interfaces
export function useTypographyPerformance() {
  const [metrics, setMetrics] = useState({
    fontLoadTime: 0,
    renderTime: 0,
    layoutShifts: 0,
    averageFPS: 60,
    fontRenderingTime: 0,
    criticalTextVisible: false,
    fontSubsetSize: 0,
    textRenderingOptimization: 'auto'
  })
  
  const [isOptimized, setIsOptimized] = useState(false)
  
  useEffect(() => {
    let frameCount = 0
    let lastTime = performance.now()
    let animationId: number
    const startTime = performance.now()
    
    // Enhanced FPS monitoring with medical interface optimization
    const measureFPS = () => {
      frameCount++
      const currentTime = performance.now()
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        setMetrics(prev => ({ ...prev, averageFPS: fps }))
        
        // Optimize based on performance
        if (fps < 30 && !isOptimized) {
          // Enable performance optimizations for medical interfaces
          document.documentElement.style.setProperty('--text-rendering', 'geometricPrecision')
          document.documentElement.classList.add('medical-text-efficient')
          setIsOptimized(true)
          setMetrics(prev => ({ ...prev, textRenderingOptimization: 'performance' }))
        } else if (fps > 55 && isOptimized) {
          // Restore quality when performance improves
          document.documentElement.style.setProperty('--text-rendering', 'optimizeLegibility')
          document.documentElement.classList.remove('medical-text-efficient')
          setIsOptimized(false)
          setMetrics(prev => ({ ...prev, textRenderingOptimization: 'quality' }))
        }
        
        frameCount = 0
        lastTime = currentTime
      }
      
      animationId = requestAnimationFrame(measureFPS)
    }
    
    animationId = requestAnimationFrame(measureFPS)
    
    // Enhanced layout shift monitoring for medical forms
    const layoutShiftObserver = new PerformanceObserver((list) => {
      let totalShifts = 0
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
          totalShifts += (entry as any).value
          
          // Critical for medical interfaces - warn about significant shifts
          if ((entry as any).value > 0.1) {
            console.warn('Significant layout shift detected in medical interface:', entry)
          }
        }
      }
      setMetrics(prev => ({ ...prev, layoutShifts: prev.layoutShifts + totalShifts }))
    })
    
    layoutShiftObserver.observe({ entryTypes: ['layout-shift'] })
    
    // Monitor font loading performance
    const fontObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name.includes('font') || entry.entryType === 'resource') {
          const resource = entry as PerformanceResourceTiming
          if (resource.name.includes('.otf') || resource.name.includes('.ttf')) {
            setMetrics(prev => ({ 
              ...prev, 
              fontLoadTime: resource.loadEventEnd - resource.loadEventStart,
              fontSubsetSize: resource.transferSize || 0
            }))
          }
        }
      }
    })
    
    fontObserver.observe({ entryTypes: ['resource'] })
    
    // Monitor critical text visibility (LCP for text)
    const navigationObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'largest-contentful-paint') {
          const lcp = entry as PerformanceEntry
          setMetrics(prev => ({ 
            ...prev, 
            fontRenderingTime: lcp.startTime,
            criticalTextVisible: lcp.startTime < 2500 // Good LCP for medical interfaces
          }))
        }
      }
    })
    
    navigationObserver.observe({ entryTypes: ['largest-contentful-paint'] })
    
    return () => {
      cancelAnimationFrame(animationId)
      layoutShiftObserver.disconnect()
      fontObserver.disconnect()
      navigationObserver.disconnect()
    }
  }, [isOptimized])
  
  // Performance recommendations based on metrics
  const recommendations = useMemo(() => {
    const issues: string[] = []
    const optimizations: string[] = []
    
    if (metrics.averageFPS < 30) {
      issues.push('Low frame rate detected')
      optimizations.push('Enable font-display: swap for critical fonts')
    }
    
    if (metrics.layoutShifts > 0.1) {
      issues.push('Cumulative Layout Shift above threshold')
      optimizations.push('Preload critical fonts and define font metrics')
    }
    
    if (metrics.fontLoadTime > 1000) {
      issues.push('Slow font loading')
      optimizations.push('Use font subsetting for medical terminology')
    }
    
    if (!metrics.criticalTextVisible) {
      issues.push('Critical text visibility delayed')
      optimizations.push('Inline critical CSS for medical typography')
    }
    
    return { issues, optimizations }
  }, [metrics])
  
  return { 
    metrics, 
    recommendations, 
    isOptimized,
    performanceGrade: (
      metrics.averageFPS >= 50 && 
      metrics.layoutShifts <= 0.1 && 
      metrics.criticalTextVisible
    ) ? 'excellent' : 
    (
      metrics.averageFPS >= 30 && 
      metrics.layoutShifts <= 0.25
    ) ? 'good' : 'needs-improvement'
  }
}

// Enhanced accessibility hook for medical interfaces
export function useMedicalAccessibility() {
  const [accessibilityFeatures, setAccessibilityFeatures] = useState({
    highContrast: false,
    reducedMotion: false,
    screenReaderActive: false,
    fontSize: 16,
    focusVisible: true,
    colorBlindnessMode: 'none' as 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia'
  })
  
  const [announcements, setAnnouncements] = useState<string[]>([])
  
  useEffect(() => {
    // Detect user preferences
    const updateAccessibilityPreferences = () => {
      setAccessibilityFeatures(prev => ({
        ...prev,
        highContrast: window.matchMedia('(prefers-contrast: high)').matches,
        reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        fontSize: parseInt(getComputedStyle(document.documentElement).fontSize) || 16
      }))
    }
    
    updateAccessibilityPreferences()
    
    // Listen for preference changes
    const contrastQuery = window.matchMedia('(prefers-contrast: high)')
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    
    contrastQuery.addEventListener('change', updateAccessibilityPreferences)
    motionQuery.addEventListener('change', updateAccessibilityPreferences)
    
    // Screen reader detection
    const detectScreenReader = () => {
      // Simple heuristic for screen reader detection
      const isScreenReader = window.navigator.userAgent.includes('NVDA') ||
                           window.navigator.userAgent.includes('JAWS') ||
                           window.speechSynthesis?.getVoices().length > 0
      
      setAccessibilityFeatures(prev => ({ ...prev, screenReaderActive: isScreenReader }))
    }
    
    detectScreenReader()
    
    return () => {
      contrastQuery.removeEventListener('change', updateAccessibilityPreferences)
      motionQuery.removeEventListener('change', updateAccessibilityPreferences)
    }
  }, [])
  
  // Announce function for screen readers
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    setAnnouncements(prev => [...prev, message])
    
    // Create live region for announcement
    const liveRegion = document.createElement('div')
    liveRegion.setAttribute('aria-live', priority)
    liveRegion.setAttribute('aria-atomic', 'true')
    liveRegion.className = 'sr-only'
    liveRegion.textContent = message
    
    document.body.appendChild(liveRegion)
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(liveRegion)
      setAnnouncements(prev => prev.filter(a => a !== message))
    }, 1000)
  }, [])
  
  // Focus management for medical forms
  const enhanceFocusVisibility = useCallback(() => {
    const style = document.createElement('style')
    style.textContent = `
      .medical-focus-enhanced *:focus {
        outline: 3px solid #84CC16;
        outline-offset: 3px;
        box-shadow: 0 0 0 6px rgba(132, 204, 22, 0.2);
        transition: box-shadow 150ms ease-out;
      }
      
      .medical-focus-enhanced .medical-critical:focus {
        outline-color: #DC2626;
        box-shadow: 0 0 0 6px rgba(220, 38, 38, 0.3);
      }
    `
    document.head.appendChild(style)
    document.documentElement.classList.add('medical-focus-enhanced')
    
    return () => {
      document.head.removeChild(style)
      document.documentElement.classList.remove('medical-focus-enhanced')
    }
  }, [])
  
  return {
    accessibilityFeatures,
    announce,
    enhanceFocusVisibility,
    isAccessible: accessibilityFeatures.highContrast || accessibilityFeatures.screenReaderActive,
    cssVariables: {
      '--a11y-font-size': `${accessibilityFeatures.fontSize}px`,
      '--a11y-motion': accessibilityFeatures.reducedMotion ? 'none' : 'auto',
      '--a11y-contrast': accessibilityFeatures.highContrast ? '1.5' : '1'
    }
  }
}

// Mobile-optimized typography hook for medical professionals
export function useMobileTypographyOptimization() {
  const [mobileOptimizations, setMobileOptimizations] = useState({
    isMobile: false,
    isLandscape: false,
    touchFriendly: false,
    screenSize: 'desktop' as 'mobile' | 'tablet' | 'desktop',
    fontSizeMultiplier: 1,
    lineHeightAdjustment: 0,
    touchTargetSize: 44
  })
  
  useEffect(() => {
    const updateMobileOptimizations = () => {
      const isMobile = window.innerWidth <= 768
      const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024
      const isLandscape = window.innerWidth > window.innerHeight
      const isTouchDevice = 'ontouchstart' in window
      
      let screenSize: 'mobile' | 'tablet' | 'desktop' = 'desktop'
      let fontSizeMultiplier = 1
      let lineHeightAdjustment = 0
      let touchTargetSize = 44
      
      if (isMobile) {
        screenSize = 'mobile'
        fontSizeMultiplier = 1.1 // Slightly larger for mobile medical professionals
        lineHeightAdjustment = 0.1
        touchTargetSize = 48 // Larger touch targets for medical accuracy
      } else if (isTablet) {
        screenSize = 'tablet'
        fontSizeMultiplier = 1.05
        lineHeightAdjustment = 0.05
        touchTargetSize = 46
      }
      
      // Special handling for landscape medical tablets
      if (isLandscape && screenSize !== 'desktop') {
        fontSizeMultiplier *= 0.95 // Slightly smaller to fit more content
        lineHeightAdjustment -= 0.05
      }
      
      setMobileOptimizations({
        isMobile,
        isLandscape,
        touchFriendly: isTouchDevice,
        screenSize,
        fontSizeMultiplier,
        lineHeightAdjustment,
        touchTargetSize
      })
    }
    
    updateMobileOptimizations()
    
    // Update on resize and orientation change
    window.addEventListener('resize', updateMobileOptimizations)
    window.addEventListener('orientationchange', updateMobileOptimizations)
    
    return () => {
      window.removeEventListener('resize', updateMobileOptimizations)
      window.removeEventListener('orientationchange', updateMobileOptimizations)
    }
  }, [])
  
  // Apply mobile-specific typography optimizations
  const applyMobileOptimizations = useCallback(() => {
    if (mobileOptimizations.isMobile) {
      // Prevent zoom on input focus (medical forms)
      const metaViewport = document.querySelector('meta[name="viewport"]')
      if (metaViewport) {
        metaViewport.setAttribute('content', 
          'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no'
        )
      }
      
      // Add mobile-specific CSS classes
      document.documentElement.classList.add('mobile-medical-interface')
      
      // Thai text optimization for mobile
      const style = document.createElement('style')
      style.textContent = `
        .mobile-medical-interface .thai-text {
          line-height: 1.8;
          word-break: keep-all;
          overflow-wrap: break-word;
        }
        
        .mobile-medical-interface .medical-form-field {
          min-height: ${mobileOptimizations.touchTargetSize}px;
          font-size: 16px; /* Prevent iOS zoom */
        }
        
        .mobile-medical-interface .medical-critical {
          font-size: calc(1rem * ${mobileOptimizations.fontSizeMultiplier});
          padding: 12px 16px;
          margin: 8px 0;
        }
      `
      document.head.appendChild(style)
      
      return () => {
        document.head.removeChild(style)
        document.documentElement.classList.remove('mobile-medical-interface')
      }
    }
  }, [mobileOptimizations])
  
  useEffect(() => {
    const cleanup = applyMobileOptimizations()
    return cleanup
  }, [applyMobileOptimizations])
  
  return {
    mobileOptimizations,
    isMobileOptimized: mobileOptimizations.isMobile,
    touchTargetSize: mobileOptimizations.touchTargetSize,
    cssVariables: {
      '--mobile-font-multiplier': mobileOptimizations.fontSizeMultiplier,
      '--mobile-line-height-adjust': mobileOptimizations.lineHeightAdjustment,
      '--mobile-touch-target': `${mobileOptimizations.touchTargetSize}px`
    }
  }
}

// Critical font loading hook with preconnect optimization
export function useCriticalFontLoading() {
  const [fontStatus, setFontStatus] = useState({
    fcIconicLoaded: false,
    fallbackActive: false,
    loadingProgress: 0,
    criticalSubsetReady: false,
    performanceOptimized: false
  })
  
  useEffect(() => {
    const loadCriticalFonts = async () => {
      try {
        // Preconnect to font resources
        const preconnectLink = document.createElement('link')
        preconnectLink.rel = 'preconnect'
        preconnectLink.href = '/fonts'
        preconnectLink.crossOrigin = 'anonymous'
        document.head.appendChild(preconnectLink)
        
        setFontStatus(prev => ({ ...prev, loadingProgress: 10 }))
        
        // Load critical subset first (Latin + Thai numerals + common medical terms)
        const criticalChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ผู้ป่วยแพทย์โรงพยาบาลยาการรักษาอาการโรค'
        await document.fonts.load('400 16px FC Iconic', criticalChars)
        
        setFontStatus(prev => ({ 
          ...prev, 
          loadingProgress: 40,
          criticalSubsetReady: true
        }))
        
        // Load full character set
        await document.fonts.load('400 16px FC Iconic')
        setFontStatus(prev => ({ ...prev, loadingProgress: 70 }))
        
        // Load bold weight
        await document.fonts.load('700 16px FC Iconic')
        setFontStatus(prev => ({ ...prev, loadingProgress: 100 }))
        
        // Verify font is actually loaded
        if (document.fonts.check('16px FC Iconic')) {
          setFontStatus(prev => ({ ...prev, fcIconicLoaded: true }))
          
          // Add loaded class for CSS optimization
          document.documentElement.classList.add('fc-iconic-loaded')
          
          // Dispatch custom event for other components
          document.dispatchEvent(new CustomEvent('fontsLoaded', {
            detail: { fontFamily: 'FC Iconic', loadTime: performance.now() }
          }))
        }
        
      } catch (error) {
        console.warn('Critical font loading failed:', error)
        setFontStatus(prev => ({ 
          ...prev, 
          fallbackActive: true,
          performanceOptimized: true
        }))
        
        // Apply fallback optimizations
        document.documentElement.classList.add('font-fallback-active')
      }
    }
    
    loadCriticalFonts()
  }, [])
  
  return {
    fontStatus,
    isReady: fontStatus.fcIconicLoaded || fontStatus.criticalSubsetReady,
    cssVariables: {
      '--font-loading-progress': `${fontStatus.loadingProgress}%`,
      '--font-fallback': fontStatus.fallbackActive ? '1' : '0'
    }
  }
}