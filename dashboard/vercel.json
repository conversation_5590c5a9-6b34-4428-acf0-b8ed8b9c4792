{"version": 2, "name": "chromoforge-dashboard", "framework": "vite", "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "devCommand": "npm run dev", "public": true, "regions": ["iad1", "sfo1", "hkg1"], "build": {"env": {"NODE_ENV": "production", "NODE_OPTIONS": "--max-old-space-size=4096"}}, "headers": [{"source": "/fonts/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, X-Requested-With, Content-Type, Accept"}, {"key": "Vary", "value": "Origin"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "rewrites": [{"source": "/((?!api/).*)", "destination": "/index.html"}], "functions": {"app/dist/index.html": {"maxDuration": 10}}, "git": {"deploymentEnabled": {"main": true, "production": true}}, "github": {"enabled": true, "autoAlias": true, "autoJobCancelation": true}, "env": {"NODE_ENV": "production"}}