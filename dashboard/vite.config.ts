import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 3001,
    host: true,
    // Enhanced hot-reload for Docker environments
    watch: {
      usePolling: true,
      interval: 1000,
    },
    // Enable CSS hot-reload
    hmr: {
      port: 3001,
    },
  },
  preview: {
    port: 3001,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    assetsDir: 'assets',
    // Optimize for Vercel deployment
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          table: ['@tanstack/react-table'],
        },
        // Optimize font asset handling
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const extType = info[info.length - 1];
          
          // Handle font files specifically for Vercel
          if (/woff|woff2|ttf|otf|eot/.test(extType || '')) {
            return `fonts/[name].[hash][extname]`;
          }
          
          // Handle other assets
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/.test(extType || '')) {
            return `images/[name].[hash][extname]`;
          }
          
          // Handle CSS and JS files
          if (/css/.test(extType || '')) {
            return `css/[name].[hash][extname]`;
          }
          
          return `assets/[name].[hash][extname]`;
        },
      },
    },
    // Optimize build performance for Vercel
    target: 'es2020',
    minify: true,
    // Font asset optimization
    assetsInlineLimit: 4096, // Inline small assets, keep fonts as separate files
  },
  // Font optimization and pre-bundling
  optimizeDeps: {
    include: ['react', 'react-dom'],
    exclude: [], // Don't exclude fonts from optimization
  },
  // Asset handling for development and production
  publicDir: 'public',
  // Ensure fonts are properly handled in all environments
  assetsInclude: ['**/*.woff', '**/*.woff2', '**/*.ttf', '**/*.otf', '**/*.eot'],
})