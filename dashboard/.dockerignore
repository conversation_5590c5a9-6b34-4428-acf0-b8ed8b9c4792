# ChromoForge Dashboard Docker Ignore
# Optimizes build performance by excluding unnecessary files

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
.next/
.nuxt/
.vite/

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Temporary folders
tmp/
temp/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Git
.git/
.gitignore

# Documentation
README.md
CHANGELOG.md
*.md

# Tests
src/**/*.test.ts
src/**/*.test.tsx
src/**/*.spec.ts
src/**/*.spec.tsx
tests/
__tests__/
test/
*.test.js
*.spec.js

# Storybook
.storybook/
storybook-static/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# TypeScript cache
*.tsbuildinfo

# Optional CSS preprocessor cache
.sass-cache/

# Tailwind CSS cache
.tailwindcss-cache

# Parcel cache
.parcel-cache/

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional stylelint cache
.stylelintcache

# Local environment files that shouldn't be in container
.env
.env.*

# ChromoForge specific ignores
../original-pdf-examples/
../test-results/
../batch-results/
../processed/
../temp/
../logs/

# Docker related
Dockerfile
.dockerignore
docker-compose*.yml