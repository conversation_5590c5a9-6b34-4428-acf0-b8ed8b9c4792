/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        // ChromoForge Design System Colors
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
          50: '#F0FDFA',   // Mint Tint
          100: '#CCFBF1',  // Light Mint
          200: '#99F6E4',  // 
          300: '#5EEAD4',  // Mint Gradient
          400: '#2DD4BF',  // Medium Teal
          500: '#14B8A6',  // ChromaForge Teal
          600: '#0D9488',  // 
          700: '#0F766E',  // Deep Medical Teal
          800: '#115E59',  // 
          900: '#134E4A',  // Darkest Teal
        },
        // Accent Colors
        lime: {
          DEFAULT: '#84CC16', // BioLime
          50: '#F7FEE7',
          100: '#ECFCCB',
          200: '#D9F99D',
          300: '#BEF264',
          400: '#A3E635',
          500: '#84CC16',
          600: '#65A30D',
          700: '#4D7C0F',
          800: '#3F6212',
          900: '#365314',
        },
        amber: {
          DEFAULT: '#F59E0B', // Genome Gold
          50: '#FFFBEB',
          100: '#FEF3C7',
          200: '#FDE68A',
          300: '#FCD34D',
          400: '#FBBF24',
          500: '#F59E0B',
          600: '#D97706',
          700: '#B45309',
          800: '#92400E',
          900: '#78350F',
        },
        // Professional Grays
        neutral: {
          50: '#FAFAFA',
          100: '#F3F4F6',   // Clinical Silver
          200: '#E5E7EB',
          300: '#D1D5DB',
          400: '#9CA3AF',
          500: '#6B7280',   // Lab Gray
          600: '#4B5563',
          700: '#374151',   // Research Charcoal
          800: '#1F2937',
          900: '#111827',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        // FC Iconic - Primary brand font with Thai support
        sans: ['FC Iconic', 'Noto Sans Thai', 'Noto Sans', 'system-ui', '-apple-system', 'sans-serif'],
        iconic: ['FC Iconic', 'Noto Sans Thai', 'Noto Sans', 'system-ui', 'sans-serif'],
        // Legacy support for existing components
        logo: ['FC Iconic', 'Noto Sans Thai', 'Noto Sans', 'system-ui', 'sans-serif'],
      },
      // FC Iconic Font Weight System
      fontWeight: {
        regular: '400',    // 80% of interface - body text, navigation, forms
        bold: '700',       // 20% of interface - headings, CTAs, emphasis
      },
      // Typography Scale following Design System
      fontSize: {
        'xs': ['12px', { lineHeight: '1.5', letterSpacing: '0.02em' }],
        'sm': ['14px', { lineHeight: '1.6', letterSpacing: '0.01em' }],
        'base': ['16px', { lineHeight: '1.6', letterSpacing: '0.005em' }],
        'lg': ['18px', { lineHeight: '1.7', letterSpacing: '0.005em' }],
        'xl': ['20px', { lineHeight: '1.7', letterSpacing: '0.01em' }],
        '2xl': ['24px', { lineHeight: '1.4', letterSpacing: '0em' }],
        '3xl': ['30px', { lineHeight: '1.3', letterSpacing: '0em' }],
        '4xl': ['36px', { lineHeight: '1.2', letterSpacing: '-0.005em' }],
        '5xl': ['48px', { lineHeight: '1.1', letterSpacing: '-0.01em' }],
        '6xl': ['56px', { lineHeight: '1.1', letterSpacing: '-0.015em' }],
        '7xl': ['72px', { lineHeight: '0.9', letterSpacing: '-0.02em' }],
      },
      // Letter Spacing System
      letterSpacing: {
        tighter: '-0.02em',
        tight: '-0.01em',
        normal: '0',
        wide: '0.01em',
        wider: '0.02em',
        widest: '0.05em',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
      boxShadow: {
        'card': '0 2px 8px rgba(0,0,0,0.08)',
        'modal': '0 8px 32px rgba(0,0,0,0.12)',
        'dropdown': '0 4px 16px rgba(0,0,0,0.10)',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
}