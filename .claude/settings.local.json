{"permissions": {"allow": ["mcp__supabase__get_project_url", "mcp__supabase__list_tables", "mcp__supabase__list_extensions", "mcp__supabase__get_anon_key", "mcp__supabase__list_edge_functions", "mcp__supabase__search_docs", "Bash(ls:*)", "Bash(find:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__serena__check_onboarding_performed", "mcp__serena__activate_project", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__search_for_pattern", "mcp__serena__write_memory", "Bash(rm:*)", "mcp__serena__read_memory", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "mcp__serena__think_about_collected_information", "mcp__serena__think_about_task_adherence", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__replace_symbol_body", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python3:*)", "mcp__serena__find_file", "mcp__serena__replace_regex", "<PERSON><PERSON>(python:*)", "Bash(grep:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(source:*)", "Bash(black:*)", "Bash(isort --version)", "Bash(isort:*)", "Bash(git init:*)", "Bash(git add:*)", "Bash(git branch:*)", "Bash(git push:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(./docker-run.sh:*)", "<PERSON><PERSON>(docker exec:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}