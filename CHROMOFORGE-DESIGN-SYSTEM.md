# ChromoForge Design System & Branding Guidelines
*Powered by FC Iconic Typography System*

## Brand Identity

### Product Name
**ChromoForge** - Advanced Genetic Solutions Platform

### Brand Personality
- **Scientific Excellence**: Precision, reliability, cutting-edge research
- **Modern Authority**: Contemporary Thai-international typography
- **Medical Trust**: Healthcare-grade security and accuracy  
- **Global Innovation**: Forward-thinking, transformative technology
- **Accessible Precision**: Professional yet approachable interface

---

## Typography System: FC Iconic Strategic Implementation

### Available Font Assets
**FC Iconic** - Professional Thai-Latin font family with strategic weight selection

### Available Weights & Files
```css
/* Available FC Iconic Weights */
--font-regular: 400;     /* FC Iconic Regular */
--font-bold: 700;        /* FC Iconic Bold */

/* Available Styles */
--style-normal: normal;  /* Regular & Bold */
--style-italic: italic;  /* Regular Italic & Bold Italic */
```

### File Type Recommendations & Strategy

#### Primary Recommendation: OTF Files
**Use .otf (OpenType) files as primary choice:**
- ✅ Superior compression and file size optimization
- ✅ Advanced typography features and better hinting
- ✅ Professional-grade rendering quality
- ✅ Better support for complex character combinations (Thai script)
- ✅ Optimal for high-quality display and print applications

#### Fallback Strategy: TTF Files
**Use .ttf (TrueType) files for broader compatibility:**
- ✅ Universal browser support (including older versions)
- ✅ System font compatibility
- ✅ Reliable rendering across all platforms
- ⚠️ Slightly larger file sizes than OTF

#### Web Optimization Strategy
**Convert OTF files to web formats for optimal performance:**
1. **WOFF2** (Primary): Best compression, modern browser support
2. **WOFF** (Fallback): Good compression, wider browser support  
3. **OTF** (Fallback): Original format for legacy support
4. **TTF** (Final Fallback): Maximum compatibility

### Strategic Weight Usage Philosophy
With only Regular (400) and Bold (700) available, hierarchy is created through intelligent combination of:

#### Typography Hierarchy Strategy
- **Size Scaling**: Primary hierarchy through font-size variation
- **Weight Contrast**: Strategic Bold vs Regular for emphasis
- **Spacing Control**: Letter-spacing and line-height for hierarchy
- **Color Differentiation**: Teal gradient system for visual weight
- **Italic Usage**: Emphasis, quotes, and subtle differentiation

#### Context-Driven Weight Selection
- **Regular (400)**: 80% of interface - body text, navigation, forms, subtle elements
- **Bold (700)**: 20% of interface - headings, CTAs, emphasis, brand elements
- **Italic Variants**: Special cases - quotes, captions, emphasis within text

---

## Logo System Revolution

### New Logo Implementation
```
CHROMO ◆ FORGE
```

### Logo Specifications
- **Font**: FC Iconic (Dynamic Weight System)
- **Structure**: Always maintain "CHROMO ◆ FORGE" format
- **Diamond Symbol**: ◆ (Unicode U+25C6) - representing precision and genetic connectivity
- **Spacing**: Single space before and after diamond
- **Case**: Always uppercase
- **Weight Strategy**: Context-driven selection

### Logo Weight Applications

#### Strategic Logo Implementation
```css
/* Hero/Landing Pages - Maximum Impact */
.logo--hero {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 700; /* Bold for maximum impact */
  font-size: 56px;
  letter-spacing: 0.02em;
  text-transform: uppercase;
}

/* Navigation Headers - Professional Presence */
.logo--header {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 700; /* Bold for clear visibility */
  font-size: 32px;
  letter-spacing: 0.01em;
}

/* Footer/Subtle Applications */
.logo--footer {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular for subtle presence */
  font-size: 24px;
  letter-spacing: 0.005em;
}

/* Compact/Mobile - Clarity Priority */
.logo--compact {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 700; /* Bold for small-size clarity */
  font-size: 20px;
  letter-spacing: 0.01em;
}

/* Alternative Styles */
.logo--light {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular for light backgrounds */
  font-style: italic; /* Italic for distinctive variation */
}
```

---

## Optimized Typography Hierarchy
*Maximizing Impact with Strategic Weight Usage*

### Display Typography
```css
/* Ultra Hero - Maximum Brand Impact */
.text-ultra-hero {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 700; /* Bold */
  font-size: 72px;
  line-height: 0.9;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  color: var(--color-teal-700);
}

/* Hero Display - Landing Pages */
.text-hero {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 700; /* Bold */
  font-size: 56px;
  line-height: 1.1;
  letter-spacing: -0.015em;
  color: var(--color-teal-700);
}

/* Display Large - Section Heroes */
.text-display-lg {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 700; /* Bold */
  font-size: 48px;
  line-height: 1.1;
  letter-spacing: -0.01em;
  color: var(--color-teal-700);
}
```

### Heading Hierarchy - Strategic Bold Usage
```css
/* H1 - Page Titles (Bold for maximum hierarchy) */
.text-h1 {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 700; /* Bold */
  font-size: 36px;
  line-height: 1.2;
  letter-spacing: -0.005em;
  color: var(--text-primary);
}

/* H2 - Major Sections (Bold with size differentiation) */
.text-h2 {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 700; /* Bold */
  font-size: 30px;
  line-height: 1.3;
  letter-spacing: 0;
  color: var(--text-primary);
}

/* H3 - Subsections (Bold, smaller size) */
.text-h3 {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 700; /* Bold */
  font-size: 24px;
  line-height: 1.4;
  letter-spacing: 0;
  color: var(--text-secondary);
}

/* H4 - Component Titles (Regular with size emphasis) */
.text-h4 {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular - hierarchy through size */
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.01em;
  color: var(--text-primary);
  text-transform: uppercase; /* Added transformation for hierarchy */
}

/* H5 - Card Headers (Regular with spacing) */
.text-h5 {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.02em;
  color: var(--text-primary);
  text-transform: uppercase;
}

/* H6 - Small Labels (Regular with color differentiation) */
.text-h6 {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.05em;
  color: var(--color-teal-500);
  text-transform: uppercase;
}
```

### Body Content - Regular Weight Optimization
```css
/* Lead Paragraph - Size-based hierarchy */
.text-lead {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 20px;
  line-height: 1.7;
  letter-spacing: 0.01em;
  color: var(--text-primary);
}

/* Body Large - Comfortable reading */
.text-body-lg {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 18px;
  line-height: 1.7;
  letter-spacing: 0.005em;
  color: var(--text-primary);
}

/* Body Regular - Standard interface text */
.text-body {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 16px;
  line-height: 1.6;
  letter-spacing: 0.005em;
  color: var(--text-primary);
}

/* Body Small - Secondary information */
.text-body-sm {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 14px;
  line-height: 1.6;
  letter-spacing: 0.01em;
  color: var(--text-secondary);
}

/* Caption - Supplementary text */
.text-caption {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-style: italic; /* Italic for subtle differentiation */
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.02em;
  color: var(--text-muted);
}
```

### Interactive Elements - Strategic Bold Application
```css
/* Primary Buttons - Bold for confidence */
.btn-text-lg {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 700; /* Bold for authority */
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.01em;
  text-transform: uppercase;
}

/* Secondary Buttons - Regular with spacing */
.btn-text-md {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.02em;
  text-transform: uppercase;
}

/* Ghost/Text Buttons - Regular, subtle */
.btn-text-sm {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.03em;
  text-transform: uppercase;
}

/* Navigation - Regular for comfortable scanning */
.nav-text {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.01em;
}

/* Active Navigation State - Bold for emphasis */
.nav-text--active {
  font-weight: 700; /* Bold for active state */
}

/* Form Labels - Regular with emphasis techniques */
.label-text {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 14px;
  line-height: 1.4;
  letter-spacing: 0.02em;
  text-transform: uppercase;
  color: var(--text-secondary);
}

/* Input Text - Regular, optimized for reading */
.input-text {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.005em;
}

/* Placeholder Text - Italic for differentiation */
.placeholder-text {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-style: italic; /* Italic for subtle indication */
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.005em;
  color: var(--text-muted);
}
```

### Emphasis and Special Cases
```css
/* Strong Emphasis - Bold within text */
.text-strong {
  font-weight: 700; /* Bold */
}

/* Subtle Emphasis - Italic within text */
.text-emphasis {
  font-style: italic;
  color: var(--color-teal-500);
}

/* Quotes - Italic with size increase */
.text-quote {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-style: italic;
  font-size: 18px;
  line-height: 1.6;
  letter-spacing: 0.01em;
  color: var(--text-secondary);
}

/* Code/Technical Text - Regular with spacing */
.text-code {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 14px;
  line-height: 1.4;
  letter-spacing: 0.05em;
  background: var(--color-teal-50);
  padding: 2px 6px;
  border-radius: 4px;
}
```

---

## Advanced Color Palette
*Optimized for FC Iconic Font Rendering*

### Primary Colors (Unchanged from medical teal system)
- **Deep Medical Teal**: `#0F766E` (rgb(15, 118, 110))
- **ChromaForge Teal**: `#14B8A6` (rgb(20, 184, 166))
- **Mint Gradient**: `#5EEAD4` (rgb(94, 234, 212))
- **BioLime**: `#84CC16` (rgb(132, 204, 22))
- **Genome Gold**: `#F59E0B` (rgb(245, 158, 11))

### Typography-Optimized Color Applications
```css
/* High Contrast Text Combinations for FC Iconic */
:root {
  /* Primary Text Colors */
  --text-primary: #134E4A;     /* Dark teal for maximum readability */
  --text-secondary: #0F766E;   /* Medium teal for emphasis */
  --text-muted: #64748B;       /* Neutral gray for supporting text */
  --text-subtle: #94A3B8;      /* Light gray for captions */
  
  /* Interactive Text Colors */
  --text-link: #14B8A6;        /* Teal for links */
  --text-link-hover: #0F766E;  /* Darker on hover */
  --text-button: #FFFFFF;      /* White on colored buttons */
  
  /* Status Text Colors */
  --text-success: #059669;     /* Success messages */
  --text-warning: #D97706;     /* Warning messages */
  --text-error: #DC2626;       /* Error messages */
  --text-info: #0369A1;        /* Information messages */
}
```

---

## Component System Updates

### Enhanced Button System
```css
/* Primary Button - ExtraBold Weight for Impact */
.btn-primary {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 600; /* SemiBold for confident clicking */
  font-size: 16px;
  background: linear-gradient(135deg, #0F766E 0%, #14B8A6 100%);
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  padding: 14px 28px;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(15, 118, 110, 0.2);
}

/* Secondary Button - Medium Weight for Balance */
.btn-secondary {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 500; /* Medium for understated confidence */
  font-size: 16px;
  border: 2px solid #14B8A6;
  color: #0F766E;
  background: transparent;
  border-radius: 8px;
  padding: 12px 26px;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
}

/* Ghost Button - Light Weight for Subtlety */
.btn-ghost {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular for gentle presence */
  font-size: 16px;
  color: #14B8A6;
  background: transparent;
  border: none;
  padding: 12px 20px;
  letter-spacing: 0.005em;
  transition: all 0.2s ease;
}
```

### Advanced Card Components
```css
/* Feature Card - Dynamic Weight Hierarchy */
.card-feature {
  background: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.card-feature__title {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 600; /* SemiBold for clear hierarchy */
  font-size: 24px;
  line-height: 1.3;
  color: #0F766E;
  margin-bottom: 12px;
}

.card-feature__description {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 300; /* Light for comfortable reading */
  font-size: 16px;
  line-height: 1.6;
  color: #64748B;
  margin-bottom: 20px;
}

.card-feature__cta {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 500; /* Medium for action confidence */
  font-size: 14px;
  color: #14B8A6;
  text-decoration: none;
  letter-spacing: 0.01em;
}
```

### Navigation System Enhancement
```css
/* Main Navigation */
.nav-main {
  background: linear-gradient(135deg, #0F766E 0%, #14B8A6 100%);
  padding: 16px 0;
  box-shadow: 0 2px 8px rgba(15, 118, 110, 0.1);
}

.nav-main__item {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 500; /* Medium for confident navigation */
  font-size: 16px;
  color: #FFFFFF;
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 6px;
  letter-spacing: 0.005em;
  transition: all 0.2s ease;
}

.nav-main__item:hover,
.nav-main__item--active {
  font-weight: 600; /* SemiBold for active state emphasis */
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

/* Breadcrumb Navigation */
.breadcrumb {
  font-family: 'FC Iconic', sans-serif;
  font-weight: 400; /* Regular for neutral guidance */
  font-size: 14px;
  color: #64748B;
  letter-spacing: 0.01em;
}

.breadcrumb__current {
  font-weight: 500; /* Medium to highlight current location */
  color: #0F766E;
}
```

---

## Advanced Layout Specifications

### Container System with Typography Optimization
```css
/* Maximum width containers optimized for FC Iconic readability */
.container-hero {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.container-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.container-text {
  max-width: 760px; /* Optimized for FC Iconic character width */
  margin: 0 auto;
  padding: 0 24px;
}

.container-narrow {
  max-width: 640px;
  margin: 0 auto;
  padding: 0 24px;
}
```

### Responsive Typography Scale
```css
/* Mobile-First Responsive Typography */
@media (max-width: 640px) {
  .text-hero { font-size: 36px; font-weight: 800; }
  .text-h1 { font-size: 28px; font-weight: 700; }
  .text-h2 { font-size: 24px; font-weight: 600; }
  .text-body { font-size: 16px; font-weight: 300; }
  
  .logo--header { font-size: 24px; font-weight: 700; }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .text-hero { font-size: 48px; font-weight: 800; }
  .text-h1 { font-size: 32px; font-weight: 700; }
  .text-h2 { font-size: 28px; font-weight: 600; }
  
  .logo--header { font-size: 28px; font-weight: 600; }
}

@media (min-width: 1025px) {
  .text-ultra-hero { font-size: 72px; font-weight: 900; }
  .text-hero { font-size: 56px; font-weight: 800; }
  .text-h1 { font-size: 36px; font-weight: 700; }
  
  .logo--hero { font-size: 56px; font-weight: 800; }
}
```

---

## Accessibility Excellence

### FC Iconic Accessibility Optimization
```css
/* Enhanced Focus States for FC Iconic */
.focus-ring {
  outline: 2px solid #84CC16;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(132, 204, 22, 0.1);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .text-primary { color: #000000; }
  .text-secondary { color: #1F2937; }
  .btn-primary { 
    background: #000000;
    border: 2px solid #000000;
    font-weight: 700; /* Bold for maximum clarity */
  }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Enhanced Readability Settings */
@media (prefers-reduced-data: reduce) {
  .text-body, .text-body-lg {
    font-weight: 400; /* Regular for better low-bandwidth rendering */
  }
}
```

### Screen Reader Optimization
```css
/* Screen Reader Specific Styles */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 8px;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
  font-family: 'FC Iconic', sans-serif;
  font-weight: 600; /* SemiBold for screen reader announcement clarity */
  background: #84CC16;
  color: #000000;
}
```

---

## Implementation Guidelines

### CSS Custom Properties System
```css
:root {
  /* FC Iconic Font Stack */
  --font-primary: 'FC Iconic', 'Noto Sans Thai', 'Noto Sans', system-ui, sans-serif;
  
  /* Dynamic Font Weights */
  --weight-thin: 100;
  --weight-extralight: 200;
  --weight-light: 300;
  --weight-regular: 400;
  --weight-medium: 500;
  --weight-semibold: 600;
  --weight-bold: 700;
  --weight-extrabold: 800;
  --weight-black: 900;
  
  /* Contextual Weight Applications */
  --weight-body: var(--weight-light);
  --weight-ui: var(--weight-medium);
  --weight-heading: var(--weight-semibold);
  --weight-display: var(--weight-bold);
  --weight-hero: var(--weight-extrabold);
  --weight-brand: var(--weight-black);
  
  /* Typography Scale */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  --text-4xl: 36px;
  --text-5xl: 48px;
  --text-6xl: 56px;
  --text-7xl: 72px;
  
  /* Line Heights */
  --leading-tight: 1.1;
  --leading-snug: 1.2;
  --leading-normal: 1.5;
  --leading-relaxed: 1.6;
  --leading-loose: 1.7;
  
  /* Letter Spacing */
  --tracking-tighter: -0.02em;
  --tracking-tight: -0.01em;
  --tracking-normal: 0;
  --tracking-wide: 0.01em;
  --tracking-wider: 0.02em;
}
```

### Font Loading Strategy
```css
/* Critical Font Loading */
@font-face {
  font-family: 'FC Iconic';
  src: url('/fonts/fc-iconic-regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'FC Iconic';
  src: url('/fonts/fc-iconic-medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'FC Iconic';
  src: url('/fonts/fc-iconic-semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Progressive Enhancement for Additional Weights */
@font-face {
  font-family: 'FC Iconic';
  src: url('/fonts/fc-iconic-light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: optional;
}

@font-face {
  font-family: 'FC Iconic';
  src: url('/fonts/fc-iconic-bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: optional;
}
```

---

## Quality Assurance Standards

### Typography Checklist
- [ ] FC Iconic loaded with proper fallbacks
- [ ] Dynamic font weights applied contextually
- [ ] Logo maintains "CHROMO ◆ FORGE" format
- [ ] All text maintains minimum 4.5:1 contrast ratio
- [ ] Font weights optimized for readability at each size
- [ ] Mobile typography scaling implemented
- [ ] Screen reader compatibility verified
- [ ] International character support confirmed

### Performance Optimization
- [ ] Critical font weights loaded first (400, 500, 600)
- [ ] Additional weights loaded progressively
- [ ] Font subsetting implemented for faster loading
- [ ] Proper font-display strategies applied
- [ ] Total font payload under 200KB

---

## International & Cultural Considerations

### Thai Language Optimization
- **FC Iconic Excellence**: Native Thai font design ensures perfect character rendering
- **Cultural Appropriateness**: Professional medical terminology support
- **Character Spacing**: Optimized for Thai script combinations
- **Readability**: Superior hinting for screen display

### Global Implementation
- **Latin Script**: Full Latin 2+ support for international markets
- **Scientific Nomenclature**: Complete Unicode support for genetic terminology
- **Multi-Script Harmony**: Seamless Thai-English mixing in interface
- **Cultural Trust**: Thai typography heritage builds regional credibility

---

*This FC Iconic-powered design system represents the pinnacle of modern Thai-international typography, combining traditional craftsmanship with cutting-edge digital optimization for the ChromoForge genetic solutions platform.*